import { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { validateEmailDomain } from '@/lib/email-validator';
import { ArrowLeft, ArrowRight, CircleCheck, AlertCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useErrorDialog } from '@/hooks/use-error-dialog';
import { useConfirmationDialog } from '@/hooks/use-confirmation-dialog';
import { ContactForm } from '@/components/ContactForm';
import LanguageSwitcher, { useTranslation } from '@/components/LanguageSwitcher';
import { useTranslationService } from '@/services/translation';

// Validation schema
const createFormSchema = (requireAllowedEmail: boolean) => {
  return z.object({
    fullName: z.string().min(2, "Full name is required"),
    email: z.string()
      .email("Please enter a valid email address")
      .refine(
        (email) => validateEmailDomain(email).isValid,
        (email) => ({
          message: validateEmailDomain(email).message || "Email domain not allowed"
        })
      ),
    country: z.string().min(1, "Country is required"),
    appType: z.string().min(1, "Application type is required"),
    macAddress: z.string().optional(),
  });
};

type FormValues = {
  fullName: string;
  email: string;
  country: string;
  appType?: string;
  macAddress?: string;
};

export default function CustomCheckoutPage() {
  const [, params] = useRoute('/checkout/:slug');
  const slug = params?.slug;
  const [checkoutState, setCheckoutState] = useState<{
    status: 'loading' | 'form' | 'processing' | 'success' | 'error' | 'not-found' | 'expired';
    paypalInvoiceUrl?: string;
    paypalButtonHtml?: string;
    error?: string;
  }>({ status: 'loading' });

  const { toast } = useToast();

  // Translation hooks
  const { currentLanguage } = useTranslation();
  const { translate } = useTranslationService();

  // State for translated content
  const [translatedContent, setTranslatedContent] = useState({
    productName: '',
    productDescription: '',
    fullNameLabel: 'Full Name',
    emailLabel: 'Email Address',
    countryLabel: 'Country',
    appTypeLabel: 'Application Type',
    macAddressLabel: 'MAC Address',
    customerInfoTitle: 'Customer Information',
    proceedButton: 'Proceed with Purchase',
    loadingText: 'Loading Checkout Page',
    notFoundTitle: 'Checkout Page Not Found',
    expiredTitle: 'Checkout Page Expired'
  });

  // Query to fetch the custom checkout page
  const { data: page, isLoading, error } = useQuery({
    queryKey: [`/api/custom-checkout/public/${slug}`],
    queryFn: () => apiRequest(`/api/custom-checkout/public/${slug}`, 'GET'),
    enabled: !!slug,
    retry: false,
    onError: (error: any) => {
      if (error.status === 404) {
        if (error.message?.includes('expired')) {
          setCheckoutState({ status: 'expired' });
        } else {
          setCheckoutState({ status: 'not-found' });
        }
      } else {
        setCheckoutState({
          status: 'error',
          error: error.message || 'Failed to load checkout page'
        });
      }
    }
  });

  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(createFormSchema(!!page?.requireAllowedEmail)),
    defaultValues: {
      fullName: '',
      email: '',
      country: '',
      appType: '',
      macAddress: '',
    },
    mode: 'onChange',
  });

  // Checkout mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: FormValues) => {
      return apiRequest(`/api/custom-checkout/checkout/${slug}`, 'POST', data);
    },
    onSuccess: (data) => {
      setCheckoutState({
        status: 'success',
        paypalInvoiceUrl: data.paypalInvoiceUrl,
        paypalButtonHtml: data.paypalButtonHtml
      });
    },
    onError: (error: any) => {
      // Check if it's an email validation error
      if (error.message && error.message.includes('email')) {
        // Get the user's email from the form
        const userEmail = form.getValues('email');

        // Show contact form in error dialog
        showError(
          "",
          <ContactForm
            userEmail={userEmail}
            checkoutPageSlug={slug}
            isDialog={true}
            onSuccess={() => {
              toast({
                title: "Message Sent",
                description: "Thank you for contacting us. We'll get back to you soon.",
              });
            }}
          />
        );
        // Reset form status to allow resubmission
        setCheckoutState({ status: 'form' });
      } else {
        // Handle other errors with error dialog
        showError(
          "Checkout Error",
          <p>{error.message || 'Failed to process checkout'}</p>
        );
        setCheckoutState({
          status: 'error',
          error: error.message || 'Failed to process checkout'
        });
      }
    }
  });

  const { showConfirmation } = useConfirmationDialog();
  const { showError } = useErrorDialog();

  // Submit handler
  const onSubmit = (data: FormValues) => {
    // Custom validation for MAC address
    const requiresMac = ['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(data.appType || '');
    let hasError = false;

    if (requiresMac && !data.macAddress) {
      form.setError('macAddress', {
        type: 'manual',
        message: 'MAC address is required for this device type'
      });
      hasError = true;
    } else if (data.macAddress) {
      const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      if (!macRegex.test(data.macAddress)) {
        form.setError('macAddress', {
          type: 'manual',
          message: 'Please enter a valid MAC address in the format 00:1A:72:c9:dc:a4'
        });
        hasError = true;
      }
    }

    if (hasError) {
      return;
    }

    // Prepare the data for submission
    const formData = {
      ...data,
      // Only include macAddress if it's required or provided
      macAddress: requiresMac ? data.macAddress : undefined
    };

    // Show confirmation dialog before proceeding with purchase
    showConfirmation(
      "Confirm Your Purchase",
      <div className="space-y-3">
        {page?.confirmationMessage ? (
          <div dangerouslySetInnerHTML={{ __html: page.confirmationMessage }} />
        ) : (
          <>
            <p>You are about to purchase:</p>
            <p className="font-medium">{page?.productName}</p>
            <p>Price: {formatCurrency(page?.price || 0)}</p>
            <p className="text-sm text-muted-foreground mt-2">
              By clicking confirm, you agree to our terms and conditions.
            </p>
          </>
        )}
      </div>,
      () => {
        // This function runs when the user confirms
        setCheckoutState({ status: 'processing' });
        mutate(formData);
      },
      {
        confirmText: "Proceed with Purchase",
        cancelText: "Cancel"
      }
    );
  };

  // Handler to try again after error
  const handleTryAgain = () => {
    setCheckoutState({ status: 'form' });
  };

  // Update state when page data is loaded
  useEffect(() => {
    if (page && !isLoading) {
      setCheckoutState({ status: 'form' });
    }
  }, [page, isLoading]);

  // Inject embed code head script when page loads with embed code payment method
  useEffect(() => {
    if (page?.paymentMethod === 'embed-code' && page?.embedCodeHeadScript) {
      // Create a script element and inject it into the head
      const scriptElement = document.createElement('div');
      scriptElement.innerHTML = page.embedCodeHeadScript;

      // Extract script tags and add them to head
      const scripts = scriptElement.querySelectorAll('script');
      scripts.forEach((script) => {
        const newScript = document.createElement('script');
        if (script.src) {
          newScript.src = script.src;
        } else {
          newScript.textContent = script.textContent;
        }
        // Add any other attributes
        Array.from(script.attributes).forEach(attr => {
          if (attr.name !== 'src') {
            newScript.setAttribute(attr.name, attr.value);
          }
        });
        document.head.appendChild(newScript);
      });

      // Cleanup function to remove scripts when component unmounts
      return () => {
        scripts.forEach((script) => {
          const existingScript = document.head.querySelector(`script[src="${script.src}"]`);
          if (existingScript) {
            document.head.removeChild(existingScript);
          }
        });
      };
    }
  }, [page?.paymentMethod, page?.embedCodeHeadScript]);

  // Translation effect - translate content when language changes
  useEffect(() => {
    if (!page || !page.enableTranslation || currentLanguage === 'en') {
      // Reset to original content if translation is disabled or language is English
      setTranslatedContent({
        productName: page?.productName || '',
        productDescription: page?.productDescription || '',
        fullNameLabel: 'Full Name',
        emailLabel: 'Email Address',
        countryLabel: 'Country',
        appTypeLabel: 'Application Type',
        macAddressLabel: 'MAC Address',
        customerInfoTitle: 'Customer Information',
        proceedButton: 'Proceed with Purchase',
        loadingText: 'Loading Checkout Page',
        notFoundTitle: 'Checkout Page Not Found',
        expiredTitle: 'Checkout Page Expired'
      });
      return;
    }

    const translateContent = async () => {
      try {
        const [
          translatedProductName,
          translatedProductDescription,
          translatedFullNameLabel,
          translatedEmailLabel,
          translatedCountryLabel,
          translatedAppTypeLabel,
          translatedMacAddressLabel,
          translatedCustomerInfoTitle,
          translatedProceedButton,
          translatedLoadingText,
          translatedNotFoundTitle,
          translatedExpiredTitle
        ] = await Promise.all([
          translate(page.productName),
          translate(page.productDescription),
          translate('Full Name'),
          translate('Email Address'),
          translate('Country'),
          translate('Application Type'),
          translate('MAC Address'),
          translate('Customer Information'),
          translate('Proceed with Purchase'),
          translate('Loading Checkout Page'),
          translate('Checkout Page Not Found'),
          translate('Checkout Page Expired')
        ]);

        setTranslatedContent({
          productName: translatedProductName,
          productDescription: translatedProductDescription,
          fullNameLabel: translatedFullNameLabel,
          emailLabel: translatedEmailLabel,
          countryLabel: translatedCountryLabel,
          appTypeLabel: translatedAppTypeLabel,
          macAddressLabel: translatedMacAddressLabel,
          customerInfoTitle: translatedCustomerInfoTitle,
          proceedButton: translatedProceedButton,
          loadingText: translatedLoadingText,
          notFoundTitle: translatedNotFoundTitle,
          expiredTitle: translatedExpiredTitle
        });
      } catch (error) {
        console.error('Translation failed:', error);
      }
    };

    translateContent();
  }, [page, currentLanguage, translate]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Render checkout form or status screens
  const renderContent = () => {
    if (checkoutState.status === 'loading' || isLoading) {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6">
            <div className="animate-spin h-12 w-12 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
          <h3 className="text-xl font-semibold mb-2">Loading Checkout Page</h3>
          <p className="text-center text-muted-foreground">Please wait a moment...</p>
        </div>
      );
    } else if (checkoutState.status === 'not-found') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-destructive">
            <AlertCircle className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Checkout Page Not Found</h3>
          <p className="text-center text-muted-foreground mb-6">
            The checkout page you're looking for doesn't exist or has been removed.
          </p>
        </div>
      );
    } else if (checkoutState.status === 'expired') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-amber-500">
            <AlertTriangle className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Checkout Page Expired</h3>
          <p className="text-center text-muted-foreground mb-6">
            This checkout page has expired and is no longer available.
          </p>
        </div>
      );
    } else if (checkoutState.status === 'form' && page) {
      return (
        <div>
          <div className="mb-6">
            <div className="flex justify-between items-start">
              <h2 className={`text-2xl font-bold mb-2 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>
                {translatedContent.productName || page.productName}
              </h2>
              {page.isTrialCheckout && (
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${page?.themeMode === 'dark' ? 'bg-slate-700 text-slate-200' : 'bg-secondary text-secondary-foreground'}`}>
                  Trial Subscription
                </div>
              )}
            </div>
            <div
              className={page?.themeMode === 'dark' ? 'text-slate-300' : 'text-muted-foreground'}
              dangerouslySetInnerHTML={{ __html: translatedContent.productDescription || page.productDescription }}
            />

            {page.imageUrl && (
              <div className="mt-4 rounded-md overflow-hidden">
                <img
                  src={page.imageUrl}
                  alt={page.productName}
                  className="w-full h-auto object-cover"
                />
              </div>
            )}
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="mb-6">
                <h3 className={`text-lg font-semibold mb-4 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>
                  {translatedContent.customerInfoTitle}
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{translatedContent.fullNameLabel}</FormLabel>
                        <FormControl>
                          <Input placeholder="John Smith" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{translatedContent.emailLabel}</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Your Email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {page?.requireAllowedEmail && (
                    <div className={`mt-4 mb-4 p-4 rounded-lg border-l-4 ${
                      page?.themeMode === 'dark'
                        ? 'bg-yellow-900/20 border-yellow-500 text-yellow-200'
                        : 'bg-yellow-50 border-yellow-400 text-yellow-800'
                    }`}>
                      <div className="flex items-start">
                        <AlertTriangle className={`h-5 w-5 mr-3 mt-0.5 flex-shrink-0 ${
                          page?.themeMode === 'dark' ? 'text-yellow-400' : 'text-yellow-600'
                        }`} />
                        <div>
                          <p className="font-medium text-sm">
                            This purchase is only available to existing subscribers. Please enter the email address associated with your subscription. New users should order a test plan first. If you want to buy a second subscription just add the email you have signed with in the first time.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{translatedContent.countryLabel} <span className="text-red-500">*</span></FormLabel>
                        <FormControl>
                          <select
                            {...field}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            <option value="" disabled>Select your country</option>
                            <option value="Afghanistan">Afghanistan</option>
                            <option value="Albania">Albania</option>
                            <option value="Algeria">Algeria</option>
                            <option value="Andorra">Andorra</option>
                            <option value="Angola">Angola</option>
                            <option value="Antigua and Barbuda">Antigua and Barbuda</option>
                            <option value="Argentina">Argentina</option>
                            <option value="Armenia">Armenia</option>
                            <option value="Australia">Australia</option>
                            <option value="Austria">Austria</option>
                            <option value="Azerbaijan">Azerbaijan</option>
                            <option value="Bahamas">Bahamas</option>
                            <option value="Bahrain">Bahrain</option>
                            <option value="Bangladesh">Bangladesh</option>
                            <option value="Barbados">Barbados</option>
                            <option value="Belarus">Belarus</option>
                            <option value="Belgium">Belgium</option>
                            <option value="Belize">Belize</option>
                            <option value="Benin">Benin</option>
                            <option value="Bhutan">Bhutan</option>
                            <option value="Bolivia">Bolivia</option>
                            <option value="Bosnia and Herzegovina">Bosnia and Herzegovina</option>
                            <option value="Botswana">Botswana</option>
                            <option value="Brazil">Brazil</option>
                            <option value="Brunei">Brunei</option>
                            <option value="Bulgaria">Bulgaria</option>
                            <option value="Burkina Faso">Burkina Faso</option>
                            <option value="Burundi">Burundi</option>
                            <option value="Cabo Verde">Cabo Verde</option>
                            <option value="Cambodia">Cambodia</option>
                            <option value="Cameroon">Cameroon</option>
                            <option value="Canada">Canada</option>
                            <option value="Central African Republic">Central African Republic</option>
                            <option value="Chad">Chad</option>
                            <option value="Chile">Chile</option>
                            <option value="China">China</option>
                            <option value="Colombia">Colombia</option>
                            <option value="Comoros">Comoros</option>
                            <option value="Congo">Congo</option>
                            <option value="Costa Rica">Costa Rica</option>
                            <option value="Croatia">Croatia</option>
                            <option value="Cuba">Cuba</option>
                            <option value="Cyprus">Cyprus</option>
                            <option value="Czech Republic">Czech Republic</option>
                            <option value="Denmark">Denmark</option>
                            <option value="Djibouti">Djibouti</option>
                            <option value="Dominica">Dominica</option>
                            <option value="Dominican Republic">Dominican Republic</option>
                            <option value="Ecuador">Ecuador</option>
                            <option value="Egypt">Egypt</option>
                            <option value="El Salvador">El Salvador</option>
                            <option value="Equatorial Guinea">Equatorial Guinea</option>
                            <option value="Eritrea">Eritrea</option>
                            <option value="Estonia">Estonia</option>
                            <option value="Eswatini">Eswatini</option>
                            <option value="Ethiopia">Ethiopia</option>
                            <option value="Fiji">Fiji</option>
                            <option value="Finland">Finland</option>
                            <option value="France">France</option>
                            <option value="Gabon">Gabon</option>
                            <option value="Gambia">Gambia</option>
                            <option value="Georgia">Georgia</option>
                            <option value="Germany">Germany</option>
                            <option value="Ghana">Ghana</option>
                            <option value="Greece">Greece</option>
                            <option value="Grenada">Grenada</option>
                            <option value="Guatemala">Guatemala</option>
                            <option value="Guinea">Guinea</option>
                            <option value="Guinea-Bissau">Guinea-Bissau</option>
                            <option value="Guyana">Guyana</option>
                            <option value="Haiti">Haiti</option>
                            <option value="Honduras">Honduras</option>
                            <option value="Hungary">Hungary</option>
                            <option value="Iceland">Iceland</option>
                            <option value="India">India</option>
                            <option value="Indonesia">Indonesia</option>
                            <option value="Iran">Iran</option>
                            <option value="Iraq">Iraq</option>
                            <option value="Ireland">Ireland</option>
                            <option value="Israel">Israel</option>
                            <option value="Italy">Italy</option>
                            <option value="Jamaica">Jamaica</option>
                            <option value="Japan">Japan</option>
                            <option value="Jordan">Jordan</option>
                            <option value="Kazakhstan">Kazakhstan</option>
                            <option value="Kenya">Kenya</option>
                            <option value="Kiribati">Kiribati</option>
                            <option value="Korea, North">Korea, North</option>
                            <option value="Korea, South">Korea, South</option>
                            <option value="Kosovo">Kosovo</option>
                            <option value="Kuwait">Kuwait</option>
                            <option value="Kyrgyzstan">Kyrgyzstan</option>
                            <option value="Laos">Laos</option>
                            <option value="Latvia">Latvia</option>
                            <option value="Lebanon">Lebanon</option>
                            <option value="Lesotho">Lesotho</option>
                            <option value="Liberia">Liberia</option>
                            <option value="Libya">Libya</option>
                            <option value="Liechtenstein">Liechtenstein</option>
                            <option value="Lithuania">Lithuania</option>
                            <option value="Luxembourg">Luxembourg</option>
                            <option value="Madagascar">Madagascar</option>
                            <option value="Malawi">Malawi</option>
                            <option value="Malaysia">Malaysia</option>
                            <option value="Maldives">Maldives</option>
                            <option value="Mali">Mali</option>
                            <option value="Malta">Malta</option>
                            <option value="Marshall Islands">Marshall Islands</option>
                            <option value="Mauritania">Mauritania</option>
                            <option value="Mauritius">Mauritius</option>
                            <option value="Mexico">Mexico</option>
                            <option value="Micronesia">Micronesia</option>
                            <option value="Moldova">Moldova</option>
                            <option value="Monaco">Monaco</option>
                            <option value="Mongolia">Mongolia</option>
                            <option value="Montenegro">Montenegro</option>
                            <option value="Morocco">Morocco</option>
                            <option value="Mozambique">Mozambique</option>
                            <option value="Myanmar">Myanmar</option>
                            <option value="Namibia">Namibia</option>
                            <option value="Nauru">Nauru</option>
                            <option value="Nepal">Nepal</option>
                            <option value="Netherlands">Netherlands</option>
                            <option value="New Zealand">New Zealand</option>
                            <option value="Nicaragua">Nicaragua</option>
                            <option value="Niger">Niger</option>
                            <option value="Nigeria">Nigeria</option>
                            <option value="North Macedonia">North Macedonia</option>
                            <option value="Norway">Norway</option>
                            <option value="Oman">Oman</option>
                            <option value="Pakistan">Pakistan</option>
                            <option value="Palau">Palau</option>
                            <option value="Palestine">Palestine</option>
                            <option value="Panama">Panama</option>
                            <option value="Papua New Guinea">Papua New Guinea</option>
                            <option value="Paraguay">Paraguay</option>
                            <option value="Peru">Peru</option>
                            <option value="Philippines">Philippines</option>
                            <option value="Poland">Poland</option>
                            <option value="Portugal">Portugal</option>
                            <option value="Qatar">Qatar</option>
                            <option value="Romania">Romania</option>
                            <option value="Russia">Russia</option>
                            <option value="Rwanda">Rwanda</option>
                            <option value="Saint Kitts and Nevis">Saint Kitts and Nevis</option>
                            <option value="Saint Lucia">Saint Lucia</option>
                            <option value="Saint Vincent and the Grenadines">Saint Vincent and the Grenadines</option>
                            <option value="Samoa">Samoa</option>
                            <option value="San Marino">San Marino</option>
                            <option value="Sao Tome and Principe">Sao Tome and Principe</option>
                            <option value="Saudi Arabia">Saudi Arabia</option>
                            <option value="Senegal">Senegal</option>
                            <option value="Serbia">Serbia</option>
                            <option value="Seychelles">Seychelles</option>
                            <option value="Sierra Leone">Sierra Leone</option>
                            <option value="Singapore">Singapore</option>
                            <option value="Slovakia">Slovakia</option>
                            <option value="Slovenia">Slovenia</option>
                            <option value="Solomon Islands">Solomon Islands</option>
                            <option value="Somalia">Somalia</option>
                            <option value="South Africa">South Africa</option>
                            <option value="South Sudan">South Sudan</option>
                            <option value="Spain">Spain</option>
                            <option value="Sri Lanka">Sri Lanka</option>
                            <option value="Sudan">Sudan</option>
                            <option value="Suriname">Suriname</option>
                            <option value="Sweden">Sweden</option>
                            <option value="Switzerland">Switzerland</option>
                            <option value="Syria">Syria</option>
                            <option value="Taiwan">Taiwan</option>
                            <option value="Tajikistan">Tajikistan</option>
                            <option value="Tanzania">Tanzania</option>
                            <option value="Thailand">Thailand</option>
                            <option value="Timor-Leste">Timor-Leste</option>
                            <option value="Togo">Togo</option>
                            <option value="Tonga">Tonga</option>
                            <option value="Trinidad and Tobago">Trinidad and Tobago</option>
                            <option value="Tunisia">Tunisia</option>
                            <option value="Turkey">Turkey</option>
                            <option value="Turkmenistan">Turkmenistan</option>
                            <option value="Tuvalu">Tuvalu</option>
                            <option value="Uganda">Uganda</option>
                            <option value="Ukraine">Ukraine</option>
                            <option value="United Arab Emirates">United Arab Emirates</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="United States">United States</option>
                            <option value="Uruguay">Uruguay</option>
                            <option value="Uzbekistan">Uzbekistan</option>
                            <option value="Vanuatu">Vanuatu</option>
                            <option value="Vatican City">Vatican City</option>
                            <option value="Venezuela">Venezuela</option>
                            <option value="Vietnam">Vietnam</option>
                            <option value="Yemen">Yemen</option>
                            <option value="Zambia">Zambia</option>
                            <option value="Zimbabwe">Zimbabwe</option>
                          </select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="mb-4">
                    <label className={`block text-sm font-medium mb-1 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>Choose the application you are using: <span className="text-red-500">*</span></label>
                    <select
                      value={form.watch('appType') || ''}
                      onChange={(e) => {
                        form.setValue('appType', e.target.value);
                        // Reset MAC address when changing app type
                        if (!['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(e.target.value)) {
                          form.setValue('macAddress', '');
                        }
                      }}
                      className={`flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${page?.themeMode === 'dark' ? 'border-slate-600 bg-slate-700 text-white' : 'border-input bg-background placeholder:text-muted-foreground'}`}
                    >
                      <option value="" disabled>Choose your application</option>
                      <option value="IPTV Smarters Pro">IPTV Smarters Pro</option>
                      <option value="GSE Smart IPTV">GSE Smart IPTV</option>
                      <option value="XCIPTV">XCIPTV</option>
                      <option value="Tivimate">Tivimate</option>
                      <option value="Royal IPTV">Royal IPTV</option>
                      <option value="Smart IPTV">Smart IPTV</option>
                      <option value="Set IPTV">Set IPTV</option>
                      <option value="Net IPTV">Net IPTV</option>
                      <option value="FLIX IPTV">FLIX IPTV</option>
                      <option value="SSIPTV">SSIPTV</option>
                      <option value="Duplex IPTV">Duplex IPTV</option>
                      <option value="iPlayTV">iPlayTV</option>
                      <option value="Mytvonline">Mytvonline</option>
                      <option value="IPTV Extreme">IPTV Extreme</option>
                      <option value="VLC Player">VLC Player</option>
                      <option value="IPTV Smaer Purple player">IPTV Smaer Purple player</option>
                      <option value="Perfect IPTV Player">Perfect IPTV Player</option>
                      <option value="M3U">M3U</option>
                      <option value="MAG">MAG</option>
                      <option value="Formuler Z">Formuler Z</option>
                      <option value="Smart STB">Smart STB</option>
                      <option value="STBEMU">STBEMU</option>
                    </select>
                    {form.formState.errors.appType && (
                      <div className="text-xs text-red-500 mt-1">
                        {form.formState.errors.appType.message}
                      </div>
                    )}
                  </div>

                  {['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(form.watch('appType')) && (
                    <div className="mb-4">
                      <label className={`block text-sm font-medium mb-1 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>Your Mac Address (required)</label>
                      <input
                        type="text"
                        placeholder="00:1A:72:c9:dc:a4"
                        value={form.watch('macAddress') || ''}
                        onChange={(e) => form.setValue('macAddress', e.target.value)}
                        className={`flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${page?.themeMode === 'dark' ? 'border-slate-600 bg-slate-700 text-white placeholder:text-slate-400' : 'border-input bg-background'}`}
                      />
                      <div className={`text-xs mt-1 ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>
                        Please enter the MAC address of your device, EX: 00:1A:72:c9:dc:a4.
                      </div>
                      {form.formState.errors.macAddress && (
                        <div className="text-xs text-red-500 mt-1">
                          {form.formState.errors.macAddress.message}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className={`border-t pt-6 mb-6 ${page?.themeMode === 'dark' ? 'border-slate-600' : 'border-border'}`}>
                <div className={`flex justify-between mb-2 ${page?.themeMode === 'dark' ? 'text-slate-300' : ''}`}>
                  <span>Subtotal</span>
                  <span>{formatCurrency(page.price)}</span>
                </div>
                <div className={`flex justify-between font-bold ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>
                  <span>Total</span>
                  <span>{formatCurrency(page.price)}</span>
                </div>
              </div>

              <div className="text-center">
                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-6"
                  disabled={isPending}
                >
                  <span>{translatedContent.proceedButton}</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <p className={`text-sm mt-2 ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>
                  {page.paymentMethod === 'paypal'
                    ? "You'll receive a PayPal invoice via email to complete your purchase securely."
                    : "You'll receive a payment link via email to complete your purchase securely."}
                </p>
                {page?.requireUsername && (
                  <div className={`text-sm mt-2 flex items-start border-t pt-2 ${page?.themeMode === 'dark' ? 'text-amber-400 border-amber-800' : 'text-amber-600 border-amber-200'}`}>
                    <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium">Subscription Extension</p>
                      <p>This checkout is for existing subscribers only. If you're a new user, please order a test plan first before extending your subscription.</p>
                    </div>
                  </div>
                )}
              </div>
            </form>
          </Form>
        </div>
      );
    } else if (checkoutState.status === 'processing') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6">
            <div className="animate-spin h-12 w-12 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
          <h3 className={`text-xl font-semibold mb-2 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>Processing Your Order</h3>
          <p className={`text-center ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>We're processing your order. Please wait a moment...</p>
        </div>
      );
    } else if (checkoutState.status === 'success') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-[#27B376]">
            <CircleCheck className="h-16 w-16" />
          </div>
          <h3 className={`text-xl font-semibold mb-2 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>Order Successful!</h3>

          {/* For embed code payment method, show the embed code directly */}
          {page?.paymentMethod === 'embed-code' && checkoutState.paypalButtonHtml ? (
            <>
              <p className={`text-center mb-6 ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>
                Please complete your payment using the secure payment form below.
              </p>
              <div className="mb-6" dangerouslySetInnerHTML={{ __html: checkoutState.paypalButtonHtml }} />
            </>
          ) : /* For trial checkout pages, show the payment button directly */
          page?.isTrialCheckout ? (
            <>
              <p className={`text-center mb-6 ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>
                Please complete your payment using the button below.
              </p>
              {checkoutState.paypalButtonHtml ? (
                <div className="mb-6" dangerouslySetInnerHTML={{ __html: checkoutState.paypalButtonHtml }} />
              ) : checkoutState.paypalInvoiceUrl && (
                <div className="mb-6">
                  <Button
                    className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    asChild
                  >
                    <a href={checkoutState.paypalInvoiceUrl} target="_blank" rel="noopener noreferrer">
                      Complete Payment
                    </a>
                  </Button>
                </div>
              )}
            </>
          ) : (
            // For regular checkout pages, only show message about email
            <p className={`text-center mb-6 ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>
              We've sent a payment link to your email address. Please check your inbox to complete your purchase.
            </p>
          )}
        </div>
      );
    } else if (checkoutState.status === 'error') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-destructive">
            <AlertCircle className="h-16 w-16" />
          </div>
          <h3 className={`text-xl font-semibold mb-2 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>Something went wrong</h3>
          <p className={`text-center mb-6 ${page?.themeMode === 'dark' ? 'text-slate-400' : 'text-muted-foreground'}`}>
            {checkoutState.error || "We couldn't process your order. Please try again or contact support if the problem persists."}
          </p>
          <div className="text-center">
            <Button
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              onClick={handleTryAgain}
            >
              Try Again
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  // Apply theme mode classes with improved dark mode colors
  const themeClasses = page?.themeMode === 'dark'
    ? 'dark bg-slate-900 text-white'
    : 'bg-gray-50';

  return (
    <div className={`min-h-screen flex flex-col ${themeClasses}`}>
      <header className={`border-b py-4 ${page?.themeMode === 'dark' ? 'bg-slate-800 border-slate-700' : 'bg-white'}`}>
        <div className="container mx-auto px-4 flex justify-between items-center">
          <a href="/" className={`text-xl font-bold flex items-center ${page?.themeMode === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            {page?.headerLogo ? (
              <img
                src={page.headerLogo}
                alt="Logo"
                className="h-8 w-auto"
                onError={(e) => {
                  // Fallback to text if logo fails to load
                  e.currentTarget.style.display = 'none';
                  const textElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (textElement) textElement.style.display = 'block';
                }}
              />
            ) : null}
            <span className={page?.headerLogo ? 'hidden' : 'block'}>
              {page?.headerTitle || "PayPal Invoicer"}
            </span>
          </a>
          <div className="flex items-center gap-4">
            {page?.enableTranslation && isTranslationEnabled && (
              <LanguageSwitcher
                variant="outline"
                size="sm"
                availableLanguages={page.translationLanguages}
              />
            )}
            <div className={`text-sm ${page?.themeMode === 'dark' ? 'text-slate-300' : 'text-muted-foreground'}`}>
              Secure Checkout
            </div>
          </div>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-2xl mx-auto">
          <Card className={`shadow-md ${page?.themeMode === 'dark' ? 'bg-slate-800 border-slate-700' : ''}`}>
            <CardContent className={`p-6 md:p-8 ${page?.themeMode === 'dark' ? 'text-white' : ''}`}>
              {renderContent()}
            </CardContent>
          </Card>
        </div>
      </main>

      <footer className={`border-t py-6 ${page?.themeMode === 'dark' ? 'bg-slate-800 border-slate-700' : 'bg-white'}`}>
        <div className={`container mx-auto px-4 text-center text-sm ${page?.themeMode === 'dark' ? 'text-slate-300' : 'text-muted-foreground'}`}>
          <div className="flex flex-col items-center space-y-3">
            {page?.footerLogo && (
              <img
                src={page.footerLogo}
                alt="Footer Logo"
                className="h-6 w-auto"
                onError={(e) => {
                  // Hide logo if it fails to load
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            {page?.footerText ? (
              <p className={page?.themeMode === 'dark' ? 'text-slate-300' : ''}>{page.footerText}</p>
            ) : (
              <>
                <p className={page?.themeMode === 'dark' ? 'text-slate-300' : ''}>© {new Date().getFullYear()} PayPal Invoicer. All rights reserved.</p>
                <p className={`mt-1 ${page?.themeMode === 'dark' ? 'text-slate-400' : ''}`}>Secure payment processing provided by PayPal.</p>
              </>
            )}
          </div>
        </div>
      </footer>
    </div>
  );
}
