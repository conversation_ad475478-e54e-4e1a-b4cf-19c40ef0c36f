import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Languages, Globe, Check } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface SupportedLanguage {
  id: number;
  code: string;
  name: string;
  nativeName: string;
  enabled: boolean;
  isDefault: boolean;
  youdaoCode: string;
}

interface LanguageSwitcherProps {
  onLanguageChange?: (languageCode: string) => void;
  className?: string;
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg';
  showLabel?: boolean;
}

// API functions
const getEnabledLanguages = async (): Promise<SupportedLanguage[]> => {
  return apiRequest('/api/translation/languages/enabled', 'GET');
};

const setCustomerLanguage = async (data: { email: string; languageCode: string }) => {
  return apiRequest('/api/translation/customer-language', 'POST', data);
};

// Language detection utilities
const detectBrowserLanguage = (): string => {
  const browserLang = navigator.language || navigator.languages?.[0] || 'en';
  return browserLang.split('-')[0].toLowerCase(); // Extract language code (e.g., 'en' from 'en-US')
};

const getStoredLanguage = (): string | null => {
  return localStorage.getItem('preferred-language');
};

const setStoredLanguage = (languageCode: string): void => {
  localStorage.setItem('preferred-language', languageCode);
};

export default function LanguageSwitcher({
  onLanguageChange,
  className = '',
  variant = 'outline',
  size = 'default',
  showLabel = true,
}: LanguageSwitcherProps) {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');

  // Fetch enabled languages
  const { data: languages = [], isLoading } = useQuery({
    queryKey: ['/api/translation/languages/enabled'],
    queryFn: getEnabledLanguages,
  });

  // Set customer language mutation
  const setLanguageMutation = useMutation({
    mutationFn: setCustomerLanguage,
    onSuccess: () => {
      console.log('Customer language preference saved');
    },
    onError: (error) => {
      console.warn('Failed to save customer language preference:', error);
    },
  });

  // Initialize language on component mount
  useEffect(() => {
    if (languages.length === 0) return;

    const availableLanguageCodes = languages.map(lang => lang.code);
    let selectedLanguage = 'en'; // Default fallback

    // Priority: stored preference > browser language > default language > first available
    const storedLanguage = getStoredLanguage();
    const browserLanguage = detectBrowserLanguage();
    const defaultLanguage = languages.find(lang => lang.isDefault)?.code;

    if (storedLanguage && availableLanguageCodes.includes(storedLanguage)) {
      selectedLanguage = storedLanguage;
    } else if (availableLanguageCodes.includes(browserLanguage)) {
      selectedLanguage = browserLanguage;
    } else if (defaultLanguage) {
      selectedLanguage = defaultLanguage;
    } else if (availableLanguageCodes.length > 0) {
      selectedLanguage = availableLanguageCodes[0];
    }

    setCurrentLanguage(selectedLanguage);
    onLanguageChange?.(selectedLanguage);
  }, [languages, onLanguageChange]);

  const handleLanguageChange = (languageCode: string) => {
    setCurrentLanguage(languageCode);
    setStoredLanguage(languageCode);
    onLanguageChange?.(languageCode);

    // Save customer preference if email is available (from checkout form or session)
    const customerEmail = localStorage.getItem('customer-email');
    if (customerEmail) {
      setLanguageMutation.mutate({ email: customerEmail, languageCode });
    }
  };

  // Don't render if no languages available or only one language
  if (isLoading || languages.length <= 1) {
    return null;
  }

  const currentLang = languages.find(lang => lang.code === currentLanguage);
  const displayName = currentLang?.nativeName || currentLang?.name || 'English';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Globe className="h-4 w-4" />
          {showLabel && (
            <>
              <span className="ml-2">{displayName}</span>
              <Badge variant="secondary" className="ml-2 text-xs">
                {currentLanguage.toUpperCase()}
              </Badge>
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center gap-3">
              <span className="font-medium">{language.nativeName}</span>
              <span className="text-sm text-muted-foreground">
                {language.name}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {language.code.toUpperCase()}
              </Badge>
              {currentLanguage === language.code && (
                <Check className="h-4 w-4 text-primary" />
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Hook for using translation in components
export const useTranslation = () => {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');

  const { data: languages = [] } = useQuery({
    queryKey: ['/api/translation/languages/enabled'],
    queryFn: getEnabledLanguages,
  });

  useEffect(() => {
    const storedLanguage = getStoredLanguage();
    if (storedLanguage) {
      setCurrentLanguage(storedLanguage);
    }
  }, []);

  const changeLanguage = (languageCode: string) => {
    setCurrentLanguage(languageCode);
    setStoredLanguage(languageCode);
  };

  return {
    currentLanguage,
    changeLanguage,
    languages,
    isTranslationEnabled: languages.length > 1,
  };
};
