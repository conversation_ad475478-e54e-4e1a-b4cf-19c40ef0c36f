import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, ExternalLink, Info } from 'lucide-react';
import type { TranslationSettings } from '@/pages/admin/TranslationSettings';

const formSchema = z.object({
  youdaoAppId: z.string().optional(),
  youdaoApiKey: z.string().optional(),
  enabled: z.boolean(),
  autoDetectBrowserLanguage: z.boolean(),
  defaultLanguage: z.string().min(1, 'Default language is required'),
});

type FormValues = z.infer<typeof formSchema>;

interface GeneralSettingsTabProps {
  settings: TranslationSettings | null;
  onUpdateSettings: (data: Partial<TranslationSettings>) => void;
  isUpdating: boolean;
}

const DEFAULT_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' },
  { code: 'de', name: 'German' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'it', name: 'Italian' },
  { code: 'nl', name: 'Dutch' },
  { code: 'pl', name: 'Polish' },
  { code: 'sv', name: 'Swedish' },
  { code: 'fi', name: 'Finnish' },
  { code: 'da', name: 'Danish' },
  { code: 'no', name: 'Norwegian' },
];

export default function GeneralSettingsTab({
  settings,
  onUpdateSettings,
  isUpdating,
}: GeneralSettingsTabProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      youdaoAppId: settings?.youdaoAppId || '',
      youdaoApiKey: '', // Always start with empty for security
      enabled: settings?.enabled || false,
      autoDetectBrowserLanguage: settings?.autoDetectBrowserLanguage ?? true,
      defaultLanguage: settings?.defaultLanguage || 'en',
    },
  });

  // Update form when settings change, but preserve API key field
  React.useEffect(() => {
    if (settings) {
      form.reset({
        youdaoAppId: settings.youdaoAppId || '',
        youdaoApiKey: '', // Keep empty to avoid showing ***HIDDEN***
        enabled: settings.enabled || false,
        autoDetectBrowserLanguage: settings.autoDetectBrowserLanguage ?? true,
        defaultLanguage: settings.defaultLanguage || 'en',
      });
    }
  }, [settings, form]);

  const onSubmit = (data: FormValues) => {
    const updateData: Partial<TranslationSettings> = {
      youdaoAppId: data.youdaoAppId,
      enabled: data.enabled,
      autoDetectBrowserLanguage: data.autoDetectBrowserLanguage,
      defaultLanguage: data.defaultLanguage,
    };

    // Only include API key if it's provided (not empty)
    if (data.youdaoApiKey && data.youdaoApiKey.trim() !== '') {
      updateData.youdaoApiKey = data.youdaoApiKey;
    }

    console.log('Submitting translation settings:', {
      ...updateData,
      youdaoApiKey: updateData.youdaoApiKey ? '[PROVIDED]' : '[NOT PROVIDED]'
    });

    onUpdateSettings(updateData);
  };

  const isApiConfigured = settings?.youdaoAppId && settings?.youdaoApiKey;

  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          To enable translation features, you need to configure Youdao API credentials.
          <a
            href="https://ai.youdao.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 ml-1 text-primary hover:underline"
          >
            Get API credentials
            <ExternalLink className="h-3 w-3" />
          </a>
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Youdao API Configuration</CardTitle>
              <CardDescription>
                Configure your Youdao translation API credentials
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="youdaoAppId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>App ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your Youdao App ID"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Your Youdao application ID from the developer console
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="youdaoApiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={settings?.youdaoApiKey ? 'Leave empty to keep current API key' : 'Enter your Youdao API Key'}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {settings?.youdaoApiKey
                        ? 'API key is configured. Enter a new key to update it, or leave empty to keep current.'
                        : 'Your Youdao API secret key (will be encrypted when stored)'
                      }
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Translation Settings</CardTitle>
              <CardDescription>
                Configure how translation features work
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Enable Translation
                      </FormLabel>
                      <FormDescription>
                        Enable automatic translation of checkout pages and emails
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={!isApiConfigured}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="autoDetectBrowserLanguage"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Auto-detect Browser Language
                      </FormLabel>
                      <FormDescription>
                        Automatically detect and use the customer's browser language
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="defaultLanguage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Default Language</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select default language" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {DEFAULT_LANGUAGES.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The fallback language when auto-detection fails or is disabled
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button type="submit" disabled={isUpdating}>
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              Save Settings
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
