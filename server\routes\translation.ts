import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { translationStorage } from '../services/translation-storage';
import { isAdmin } from '../middleware/auth';

const translationRouter = Router();

// Validation schemas
const translationSettingsSchema = z.object({
  youdaoAppId: z.string().optional(),
  youdaoApiKey: z.string().optional(),
  enabled: z.boolean().optional(),
  autoDetectBrowserLanguage: z.boolean().optional(),
  defaultLanguage: z.string().optional(),
});

const languageStatusSchema = z.object({
  enabled: z.boolean(),
});

const testTranslationSchema = z.object({
  text: z.string().min(1, 'Text is required'),
  targetLanguage: z.string().min(1, 'Target language is required'),
  sourceLanguage: z.string().optional().default('en'),
});

const testConnectionSchema = z.object({
  appId: z.string().optional(),
  apiKey: z.string().optional(),
});

// Get translation settings
translationRouter.get('/settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const settings = await translationStorage.getTranslationSettings();

    // Don't expose API key in response
    const safeSettings = settings ? {
      ...settings,
      youdaoApiKey: settings.youdaoApiKey ? '***HIDDEN***' : undefined
    } : null;

    res.json(safeSettings);
  } catch (error) {
    console.error('Error fetching translation settings:', error);
    res.status(500).json({ message: 'Failed to fetch translation settings' });
  }
});

// Update translation settings
translationRouter.put('/settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = translationSettingsSchema.parse(req.body);

    console.log('Updating translation settings:', {
      ...validatedData,
      youdaoApiKey: validatedData.youdaoApiKey ? '[HIDDEN]' : undefined
    });

    const updatedSettings = await translationStorage.updateTranslationSettings(validatedData);

    // Don't expose API key in response
    const safeSettings = {
      ...updatedSettings,
      youdaoApiKey: updatedSettings.youdaoApiKey ? '***HIDDEN***' : undefined
    };

    res.json(safeSettings);
  } catch (error) {
    console.error('Error updating translation settings:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update translation settings' });
  }
});

// Get supported languages
translationRouter.get('/languages', isAdmin, async (req: Request, res: Response) => {
  try {
    const languages = await translationStorage.getSupportedLanguages();
    res.json(languages);
  } catch (error) {
    console.error('Error fetching supported languages:', error);
    res.status(500).json({ message: 'Failed to fetch supported languages' });
  }
});

// Get enabled languages (public endpoint for frontend)
translationRouter.get('/languages/enabled', async (req: Request, res: Response) => {
  try {
    const languages = await translationStorage.getEnabledLanguages();
    res.json(languages);
  } catch (error) {
    console.error('Error fetching enabled languages:', error);
    res.status(500).json({ message: 'Failed to fetch enabled languages' });
  }
});

// Update language status
translationRouter.put('/languages/:code/status', isAdmin, async (req: Request, res: Response) => {
  try {
    const { code } = req.params;
    const { enabled } = languageStatusSchema.parse(req.body);

    await translationStorage.updateLanguageStatus(code, enabled);

    res.json({ message: 'Language status updated successfully' });
  } catch (error) {
    console.error('Error updating language status:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update language status' });
  }
});

// Test translation
translationRouter.post('/test', isAdmin, async (req: Request, res: Response) => {
  try {
    const { text, targetLanguage, sourceLanguage } = testTranslationSchema.parse(req.body);

    console.log('Testing translation:', { text, sourceLanguage, targetLanguage });

    // For testing, we bypass the enabled check and directly use the API
    const settings = await translationStorage.getTranslationSettings();
    if (!settings || !settings.youdaoAppId || !settings.youdaoApiKey) {
      return res.status(400).json({
        success: false,
        message: 'Youdao API credentials not configured'
      });
    }

    // Test translation directly with API credentials
    const testResult = await translationStorage.testYoudaoConnection();
    if (!testResult.success) {
      return res.status(400).json({
        success: false,
        message: `API connection failed: ${testResult.message}`
      });
    }

    // Perform the actual translation test
    const { YoudaoTranslationService } = await import('../services/youdao-translation');
    const youdaoService = new YoudaoTranslationService({
      appId: settings.youdaoAppId,
      apiKey: settings.youdaoApiKey
    });

    const youdaoSourceLang = YoudaoTranslationService.getYoudaoLanguageCode(sourceLanguage);
    const youdaoTargetLang = YoudaoTranslationService.getYoudaoLanguageCode(targetLanguage);

    const translatedText = await youdaoService.translate(text, youdaoSourceLang, youdaoTargetLang);

    res.json({
      success: true,
      originalText: text,
      translatedText,
      sourceLanguage,
      targetLanguage
    });
  } catch (error) {
    console.error('Error testing translation:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Translation test failed'
    });
  }
});

// Test Youdao API connection
translationRouter.post('/test-connection', isAdmin, async (req: Request, res: Response) => {
  try {
    const { appId, apiKey } = testConnectionSchema.parse(req.body);

    console.log('Testing Youdao API connection');

    const result = await translationStorage.testYoudaoConnection(appId, apiKey);

    res.json(result);
  } catch (error) {
    console.error('Error testing Youdao connection:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Connection test failed'
    });
  }
});

// Initialize default languages
translationRouter.post('/initialize', isAdmin, async (req: Request, res: Response) => {
  try {
    await translationStorage.initializeDefaultLanguages();
    res.json({ message: 'Default languages initialized successfully' });
  } catch (error) {
    console.error('Error initializing default languages:', error);
    res.status(500).json({ message: 'Failed to initialize default languages' });
  }
});

// Get customer language preference
translationRouter.get('/customer-language/:email', isAdmin, async (req: Request, res: Response) => {
  try {
    const { email } = req.params;
    const customerLanguage = await translationStorage.getCustomerLanguage(email);

    res.json(customerLanguage);
  } catch (error) {
    console.error('Error fetching customer language:', error);
    res.status(500).json({ message: 'Failed to fetch customer language' });
  }
});

// Set customer language preference
translationRouter.post('/customer-language', async (req: Request, res: Response) => {
  try {
    const { email, languageCode } = req.body;

    if (!email || !languageCode) {
      return res.status(400).json({ message: 'Email and language code are required' });
    }

    const customerLanguage = await translationStorage.setCustomerLanguage(email, languageCode);

    res.json(customerLanguage);
  } catch (error) {
    console.error('Error setting customer language:', error);
    res.status(500).json({ message: 'Failed to set customer language' });
  }
});

export { translationRouter };
