import { db } from '../db';
import {
  translationSettings,
  supportedLanguages,
  translations,
  customerLanguages,
  type TranslationSettings,
  type SupportedLanguage,
  type Translation,
  type CustomerLanguage,
  type InsertTranslationSettings,
  type InsertSupportedLanguage,
  type InsertTranslation,
  type InsertCustomerLanguage
} from '@shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import crypto from 'crypto';
import { YoudaoTranslationService, DEFAULT_SUPPORTED_LANGUAGES } from './youdao-translation';

export class TranslationStorageService {
  private youdaoService: YoudaoTranslationService;

  constructor() {
    this.youdaoService = new YoudaoTranslationService();
  }

  // Translation Settings
  async getTranslationSettings(): Promise<TranslationSettings | null> {
    const result = await db.select().from(translationSettings).limit(1);
    return result[0] || null;
  }

  async updateTranslationSettings(settings: Partial<InsertTranslationSettings>): Promise<TranslationSettings> {
    const now = new Date().toISOString();
    const existing = await this.getTranslationSettings();

    // Convert boolean values to integers for SQLite
    const sqliteSettings = {
      ...settings,
      enabled: typeof settings.enabled === 'boolean' ? (settings.enabled ? 1 : 0) : settings.enabled,
      autoDetectBrowserLanguage: typeof settings.autoDetectBrowserLanguage === 'boolean' ? (settings.autoDetectBrowserLanguage ? 1 : 0) : settings.autoDetectBrowserLanguage,
    };

    if (existing) {
      const updated = await db
        .update(translationSettings)
        .set({ ...sqliteSettings, updatedAt: now })
        .where(eq(translationSettings.id, existing.id))
        .returning();

      // Update Youdao service config if credentials changed
      if (settings.youdaoAppId || settings.youdaoApiKey) {
        const newSettings = updated[0];
        if (newSettings.youdaoAppId && newSettings.youdaoApiKey) {
          this.youdaoService.setConfig({
            appId: newSettings.youdaoAppId,
            apiKey: newSettings.youdaoApiKey
          });
        }
      }

      return updated[0];
    } else {
      const created = await db
        .insert(translationSettings)
        .values({ ...sqliteSettings, createdAt: now, updatedAt: now })
        .returning();

      return created[0];
    }
  }

  // Supported Languages
  async getSupportedLanguages(): Promise<SupportedLanguage[]> {
    return await db.select().from(supportedLanguages).orderBy(supportedLanguages.name);
  }

  async getEnabledLanguages(): Promise<SupportedLanguage[]> {
    return await db
      .select()
      .from(supportedLanguages)
      .where(eq(supportedLanguages.enabled, 1)) // Use integer for SQLite
      .orderBy(supportedLanguages.name);
  }

  async updateLanguageStatus(code: string, enabled: boolean): Promise<void> {
    const now = new Date().toISOString();
    await db
      .update(supportedLanguages)
      .set({ enabled: enabled ? 1 : 0, updatedAt: now }) // Convert boolean to integer for SQLite
      .where(eq(supportedLanguages.code, code));
  }

  async initializeDefaultLanguages(): Promise<void> {
    const existing = await this.getSupportedLanguages();
    if (existing.length > 0) {
      return; // Already initialized
    }

    const now = new Date().toISOString();
    const languagesToInsert = DEFAULT_SUPPORTED_LANGUAGES.map(lang => ({
      code: lang.code,
      name: lang.name,
      nativeName: lang.nativeName,
      youdaoCode: lang.youdaoCode,
      enabled: 1, // All languages enabled by default (integer for SQLite)
      isDefault: lang.isDefault ? 1 : 0, // Convert boolean to integer for SQLite
      createdAt: now,
      updatedAt: now
    }));

    await db.insert(supportedLanguages).values(languagesToInsert);
    console.log('✅ Initialized default supported languages');
  }

  // Translation Cache
  private generateChecksum(sourceText: string, sourceLanguage: string, targetLanguage: string): string {
    return crypto
      .createHash('md5')
      .update(`${sourceText}:${sourceLanguage}:${targetLanguage}`)
      .digest('hex');
  }

  async getCachedTranslation(
    sourceText: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<Translation | null> {
    const checksum = this.generateChecksum(sourceText, sourceLanguage, targetLanguage);

    const result = await db
      .select()
      .from(translations)
      .where(
        and(
          eq(translations.checksum, checksum),
          eq(translations.sourceLanguage, sourceLanguage),
          eq(translations.targetLanguage, targetLanguage)
        )
      )
      .limit(1);

    return result[0] || null;
  }

  async cacheTranslation(
    sourceText: string,
    translatedText: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<Translation> {
    const now = new Date().toISOString();
    const checksum = this.generateChecksum(sourceText, sourceLanguage, targetLanguage);

    const translationData: InsertTranslation = {
      sourceText,
      translatedText,
      sourceLanguage,
      targetLanguage,
      checksum,
      createdAt: now,
      updatedAt: now
    };

    const result = await db.insert(translations).values(translationData).returning();
    return result[0];
  }

  // Customer Language Preferences
  async getCustomerLanguage(email: string): Promise<CustomerLanguage | null> {
    const result = await db
      .select()
      .from(customerLanguages)
      .where(eq(customerLanguages.email, email))
      .orderBy(desc(customerLanguages.lastUsed))
      .limit(1);

    return result[0] || null;
  }

  async setCustomerLanguage(email: string, languageCode: string): Promise<CustomerLanguage> {
    const now = new Date().toISOString();
    const existing = await this.getCustomerLanguage(email);

    if (existing) {
      const updated = await db
        .update(customerLanguages)
        .set({ languageCode, lastUsed: now })
        .where(eq(customerLanguages.id, existing.id))
        .returning();

      return updated[0];
    } else {
      const customerLanguageData: InsertCustomerLanguage = {
        email,
        languageCode,
        lastUsed: now,
        createdAt: now
      };

      const result = await db.insert(customerLanguages).values(customerLanguageData).returning();
      return result[0];
    }
  }

  // Translation with caching
  async translateText(
    text: string,
    targetLanguage: string,
    sourceLanguage: string = 'en'
  ): Promise<string> {
    if (!text || text.trim() === '') {
      return text;
    }

    // Check if translation is needed
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    // Check cache first
    const cached = await this.getCachedTranslation(text, sourceLanguage, targetLanguage);
    if (cached) {
      console.log('📋 Using cached translation');
      return cached.translatedText;
    }

    // Get translation settings
    const settings = await this.getTranslationSettings();
    if (!settings || !settings.enabled || !settings.youdaoAppId || !settings.youdaoApiKey) {
      console.log('⚠️ Translation service not configured or disabled');
      return text; // Return original text if translation is disabled
    }

    // Configure Youdao service
    this.youdaoService.setConfig({
      appId: settings.youdaoAppId,
      apiKey: settings.youdaoApiKey
    });

    try {
      // Get Youdao language codes
      const youdaoSourceLang = YoudaoTranslationService.getYoudaoLanguageCode(sourceLanguage);
      const youdaoTargetLang = YoudaoTranslationService.getYoudaoLanguageCode(targetLanguage);

      // Translate using Youdao
      const translatedText = await this.youdaoService.translate(text, youdaoSourceLang, youdaoTargetLang);

      // Cache the translation
      await this.cacheTranslation(text, translatedText, sourceLanguage, targetLanguage);

      console.log('🌐 Translation completed and cached');
      return translatedText;
    } catch (error) {
      console.error('❌ Translation failed:', error);
      return text; // Return original text on error
    }
  }

  // Test Youdao API connection
  async testYoudaoConnection(appId?: string, apiKey?: string): Promise<{ success: boolean; message: string; translation?: string }> {
    try {
      let config;

      if (appId && apiKey) {
        config = { appId, apiKey };
      } else {
        const settings = await this.getTranslationSettings();
        if (!settings || !settings.youdaoAppId || !settings.youdaoApiKey) {
          return {
            success: false,
            message: 'Youdao API credentials not configured'
          };
        }
        config = {
          appId: settings.youdaoAppId,
          apiKey: settings.youdaoApiKey
        };
      }

      const testService = new YoudaoTranslationService(config);
      return await testService.testConnection();
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

// Export singleton instance
export const translationStorage = new TranslationStorageService();
