import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import ImageUploader from '@/components/admin/ImageUploader';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SimpleRichTextEditor from '@/components/admin/SimpleRichTextEditor';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, AlertTriangle } from 'lucide-react';

// Form schema for custom checkout page
const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().optional(),
  productName: z.string().min(1, 'Product name is required'),
  productDescription: z.string().min(1, 'Product description is required'),
  price: z.coerce.number().positive('Price must be positive'),
  imageUrl: z.string().optional(),
  paymentMethod: z.enum(['paypal', 'custom-link', 'paypal-button-embed', 'trial-custom-link', 'trial-paypal-button-embed', 'embed-code']),
  customPaymentLinkId: z.string().optional(),
  paypalButtonId: z.string().optional(),
  trialCustomPaymentLinkId: z.string().optional(),
  trialPaypalButtonId: z.string().optional(),
  embedCodeId: z.string().optional(),
  confirmationMessage: z.string().optional(),
  headerTitle: z.string().optional(),
  footerText: z.string().optional(),
  headerLogo: z.string().optional(),
  footerLogo: z.string().optional(),
  themeMode: z.enum(['light', 'dark']).default('light'),
  useReferrerMasking: z.boolean().default(false),
  redirectDelay: z.coerce.number().min(0).max(10000).default(2000),
  smtpProviderId: z.string().optional(),
  requireAllowedEmail: z.boolean().default(false),
  isTrialCheckout: z.boolean().default(false),
  enableTranslation: z.boolean().default(false),
  translationLanguages: z.array(z.string()).optional(),
  active: z.boolean().default(true)
});

type FormValues = z.infer<typeof formSchema>;

export default function CreateCustomCheckoutPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();

  // Query to fetch payment config (for payment methods)
  const { data: paymentConfig } = useQuery({
    queryKey: ['/api/admin/payment-config'],
    queryFn: () => apiRequest('/api/admin/payment-config', 'GET')
  });

  // Query to fetch email config (for SMTP providers)
  const { data: emailConfig } = useQuery({
    queryKey: ['/api/admin/email-config'],
    queryFn: () => apiRequest('/api/admin/email-config', 'GET')
  });

  // Query to fetch embed codes
  const { data: embedCodes = [] } = useQuery({
    queryKey: ['/api/embed-codes'],
    queryFn: () => apiRequest('/api/embed-codes', 'GET')
  });

  // Query to fetch translation settings and languages
  const { data: translationSettings } = useQuery({
    queryKey: ['/api/translation/settings'],
    queryFn: () => apiRequest('/api/translation/settings', 'GET')
  });

  const { data: availableLanguages = [] } = useQuery({
    queryKey: ['/api/translation/languages/enabled'],
    queryFn: () => apiRequest('/api/translation/languages/enabled', 'GET')
  });

  // Form for creating custom checkout page
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      slug: '',
      productName: '',
      productDescription: '',
      price: 0,
      imageUrl: '',
      paymentMethod: 'paypal',
      customPaymentLinkId: '',
      paypalButtonId: '',
      trialCustomPaymentLinkId: '',
      trialPaypalButtonId: '',
      embedCodeId: '',
      confirmationMessage: '<div class="space-y-3"><p><strong>🛒 Ready to complete your purchase?</strong></p><p>✅ <strong>What you\'re getting:</strong></p><ul class="list-disc list-inside space-y-1"><li>Instant access to your product</li><li>Full customer support</li><li>Secure payment processing</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Your payment will be processed securely</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>',
      headerTitle: '',
      footerText: '',
      headerLogo: '',
      footerLogo: '',
      themeMode: 'light',
      useReferrerMasking: false,
      redirectDelay: 2000,
      smtpProviderId: 'default',
      requireAllowedEmail: false,
      isTrialCheckout: false,
      enableTranslation: false,
      translationLanguages: [],
      active: true
    }
  });

  // Get available payment methods
  const paypalProvider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal');
  const customLinkProvider = paymentConfig?.providers?.find((p: any) => p.id === 'custom-link');
  const paypalButtonEmbedProvider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal-button-embed');
  const trialCustomLinkProvider = paymentConfig?.providers?.find((p: any) => p.id === 'trial-custom-link');
  const trialPaypalButtonEmbedProvider = paymentConfig?.providers?.find((p: any) => p.id === 'trial-paypal-button-embed');

  const customLinks = customLinkProvider?.config?.links || [];
  const paypalButtons = paypalButtonEmbedProvider?.config?.buttons || [];
  const trialCustomLinks = trialCustomLinkProvider?.config?.links || [];
  const trialPaypalButtons = trialPaypalButtonEmbedProvider?.config?.buttons || [];

  // Get available SMTP providers
  const smtpProviders = emailConfig?.providers?.filter((p: any) => p.active) || [];

  // Mutation to create a new custom checkout page
  const createMutation = useMutation({
    mutationFn: (data: FormValues) => {
      console.log('Sending API request with data:', data);
      return apiRequest('/api/custom-checkout', 'POST', data);
    },
    onSuccess: () => {
      console.log('Custom checkout page created successfully');
      queryClient.invalidateQueries({ queryKey: ['/api/custom-checkout'] });
      toast({
        title: 'Success',
        description: 'Custom checkout page created successfully',
      });
      setLocation('/admin/custom-checkout');
    },
    onError: (error: any) => {
      console.error('Error creating custom checkout page:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create custom checkout page',
        variant: 'destructive',
      });
    }
  });

  // Handle form submission for creating a new page
  const onSubmit = (values: FormValues) => {
    console.log('Creating custom checkout page with values:', values);
    createMutation.mutate(values);
  };

  return (
    <AdminLayout>
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" onClick={() => setLocation('/admin/custom-checkout')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Checkout Pages
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create Custom Checkout Page</h1>
          <p className="text-muted-foreground">Create a new checkout page with custom product details and payment method</p>
        </div>
      </div>

      <Card className="max-w-4xl">
        <CardHeader>
          <CardTitle>New Checkout Page</CardTitle>
          <CardDescription>
            Fill in the details below to create your custom checkout page
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="product">Product Details</TabsTrigger>
                  <TabsTrigger value="payment">Payment Settings</TabsTrigger>
                  <TabsTrigger value="translation">Translation</TabsTrigger>
                  <TabsTrigger value="confirmation">Confirmation</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Page Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Summer Sale Offer" {...field} />
                        </FormControl>
                        <FormDescription>
                          A title for your checkout page (not visible to customers)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Slug (Optional)</FormLabel>
                        <FormControl>
                          <div className="flex items-center">
                            <span className="px-3 py-2 bg-muted border border-r-0 rounded-l-md text-sm text-muted-foreground">
                              /checkout/
                            </span>
                            <Input
                              placeholder="summer-sale"
                              {...field}
                              className="rounded-l-none"
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Custom URL path for this checkout page. Leave blank to generate automatically.
                          <br />
                          <strong>Full URL:</strong> {window.location.origin}/checkout/{field.value || 'your-slug'}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="headerTitle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Header Title (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="My Store" {...field} />
                          </FormControl>
                          <FormDescription>
                            Custom title to display in the checkout page header. Leave empty to use default.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="footerText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Footer Text (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="© 2024 My Store. All rights reserved." {...field} />
                          </FormControl>
                          <FormDescription>
                            Custom footer text to display at the bottom of the checkout page.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="headerLogo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Header Logo (Optional)</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <ImageUploader
                                initialUrl={field.value}
                                onImageUploaded={(url) => field.onChange(url)}
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Upload a logo for the checkout page header. This will replace the header title when present.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="footerLogo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Footer Logo (Optional)</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <ImageUploader
                                initialUrl={field.value}
                                onImageUploaded={(url) => field.onChange(url)}
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Upload a logo for the checkout page footer. This will appear above the footer text.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="themeMode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Theme Mode</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select theme mode" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="light">Light Mode</SelectItem>
                            <SelectItem value="dark">Dark Mode</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose the theme mode for this checkout page. This only affects the checkout page, not the admin panel.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                </TabsContent>

                <TabsContent value="product" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Premium WordPress Theme" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the product you're selling
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="productDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Description</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={200}
                            placeholder="A professionally designed WordPress theme with premium features..."
                            showPreview={false}
                          />
                        </FormControl>
                        <FormDescription>
                          Describe the product in detail. You can use formatting to make it more attractive.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            placeholder="49.99"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The price of the product in USD
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="imageUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Image (Optional)</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            <ImageUploader
                              initialUrl={field.value}
                              onImageUploaded={(url) => field.onChange(url)}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Upload an image for your product
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="isTrialCheckout"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Trial Checkout</FormLabel>
                            <FormDescription>
                              Mark this as a trial subscription checkout page
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="requireAllowedEmail"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Require Allowed Email</FormLabel>
                            <FormDescription>
                              Require customers to enter a valid email from your allowed list
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Active Status</FormLabel>
                          <FormDescription>
                            Make this checkout page available to customers
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="payment" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Method</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select payment method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Regular Payment Methods</SelectLabel>
                              {paypalProvider && (
                                <SelectItem value="paypal">
                                  PayPal Invoice {!paypalProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {customLinkProvider && (
                                <SelectItem value="custom-link">
                                  Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="paypal-button-embed">
                                  PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              <SelectItem value="embed-code">
                                External Embed Code (BillGang, SellPass, etc.)
                              </SelectItem>
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>Trial Payment Methods</SelectLabel>
                              {customLinkProvider && (
                                <SelectItem value="trial-custom-link">
                                  Trial Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="trial-paypal-button-embed">
                                  Trial PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose how customers will pay for this product
                        </FormDescription>
                        {field.value === 'paypal' && paypalProvider && !paypalProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'custom-link' && customLinkProvider && !customLinkProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>Custom Payment Link is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'paypal-button-embed' && paypalButtonEmbedProvider && !paypalButtonEmbedProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal Button Embed is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('paymentMethod') === 'custom-link' && customLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="customPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {customLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'paypal-button-embed' && paypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="paypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {paypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-custom-link' && trialCustomLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialCustomPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialCustomLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-paypal-button-embed' && trialPaypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialPaypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialPaypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'embed-code' && embedCodes.length > 0 && (
                    <FormField
                      control={form.control}
                      name="embedCodeId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>External Embed Code</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select an embed code" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {embedCodes.filter((code: any) => code.active).map((code: any) => (
                                <SelectItem key={code.id} value={code.id}>
                                  {code.name} ({code.platform})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose an external payment embed code (BillGang, SellPass, etc.)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'embed-code' && embedCodes.length === 0 && (
                    <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                      <AlertTriangle className="h-4 w-4" />
                      <span>No embed codes configured. Please add embed codes in Payment Settings first.</span>
                    </div>
                  )}

                  <Separator className="my-4" />

                  <div className="mb-2">
                    <h3 className="text-lg font-medium">Email Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure which SMTP provider to use for sending emails from this checkout page
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="smtpProviderId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SMTP Provider</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select SMTP provider (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="default">Use default SMTP provider</SelectItem>
                            {smtpProviders.map((provider: any) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                {provider.name} {provider.isDefault && "(Default)"} {provider.isBackup && "(Backup)"}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose a specific SMTP provider to use for sending emails from this checkout page.
                          If not selected, the default SMTP provider will be used.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator className="my-4" />

                  <div className="mb-2">
                    <h3 className="text-lg font-medium">Redirect Link Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure redirect links for referrer masking and traffic protection
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="useReferrerMasking"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Enable Redirect Link</FormLabel>
                          <FormDescription>
                            Generate a masked redirect link that hides the referrer and provides traffic protection
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch('useReferrerMasking') && (
                    <FormField
                      control={form.control}
                      name="redirectDelay"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Redirect Delay (milliseconds)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="10000"
                              step="100"
                              placeholder="2000"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Delay in milliseconds before redirecting to the checkout page (0-10000ms). Default is 2000ms (2 seconds).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </TabsContent>

                <TabsContent value="translation" className="space-y-4 pt-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium">Translation Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure multi-language support for this checkout page
                    </p>
                  </div>

                  {!translationSettings?.enabled && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-yellow-800">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="font-medium">Translation System Disabled</span>
                      </div>
                      <p className="text-sm text-yellow-700 mt-1">
                        The global translation system is disabled. Enable it in Translation Settings to use per-page translation controls.
                      </p>
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="enableTranslation"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Enable Translation for This Page</FormLabel>
                          <FormDescription>
                            Allow customers to switch languages on this specific checkout page
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!translationSettings?.enabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch('enableTranslation') && translationSettings?.enabled && (
                    <FormField
                      control={form.control}
                      name="translationLanguages"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Available Languages</FormLabel>
                          <FormDescription className="mb-3">
                            Select which languages customers can choose from on this checkout page.
                            If none selected, all enabled languages will be available.
                          </FormDescription>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                            {availableLanguages.map((language: any) => (
                              <div key={language.code} className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id={`lang-${language.code}`}
                                  checked={field.value?.includes(language.code) || false}
                                  onChange={(e) => {
                                    const currentLanguages = field.value || [];
                                    if (e.target.checked) {
                                      field.onChange([...currentLanguages, language.code]);
                                    } else {
                                      field.onChange(currentLanguages.filter((code: string) => code !== language.code));
                                    }
                                  }}
                                  className="rounded border-gray-300"
                                />
                                <label
                                  htmlFor={`lang-${language.code}`}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  {language.nativeName} ({language.name})
                                </label>
                              </div>
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('enableTranslation') && translationSettings?.enabled && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-blue-800">
                        <span className="font-medium">🌐 Translation Features</span>
                      </div>
                      <ul className="text-sm text-blue-700 mt-2 space-y-1">
                        <li>• Product name and description will be automatically translated</li>
                        <li>• Form labels and buttons will be translated</li>
                        <li>• Confirmation messages will be translated</li>
                        <li>• Language switcher will appear in the top-right corner</li>
                        <li>• Customer language preference will be remembered</li>
                      </ul>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="confirmation" className="space-y-4 pt-6">
                  <FormField
                    control={form.control}
                    name="confirmationMessage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirmation Message</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={300}
                            placeholder="Enter a message to show in the confirmation popup before purchase"
                            showPreview={false}
                          />
                        </FormControl>
                        <FormDescription>
                          This message will be shown to the customer in a confirmation dialog before they complete their purchase.
                          You can use HTML formatting to make it more attractive. The default message includes emojis and structured content.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="bg-muted/50 p-4 rounded-lg">
                    <h4 className="font-medium mb-2">💡 Tips for effective confirmation messages:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Use emojis to make the message more engaging</li>
                      <li>• Clearly list what the customer will receive</li>
                      <li>• Include security and trust indicators</li>
                      <li>• Keep it concise but informative</li>
                      <li>• Use HTML formatting for better presentation</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setLocation('/admin/custom-checkout')}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createMutation.isPending}
                >
                  {createMutation.isPending ? 'Creating...' : 'Create Checkout Page'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}
