import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Globe, ShoppingCart, CreditCard, Shield, Clock } from 'lucide-react';
import LanguageSwitcher, { useTranslation } from '@/components/LanguageSwitcher';
import { useTranslationService, COMMON_TRANSLATIONS } from '@/services/translation';

interface TranslatedCheckoutDemoProps {
  className?: string;
}

export default function TranslatedCheckoutDemo({ className = '' }: TranslatedCheckoutDemoProps) {
  const { currentLanguage, languages, isTranslationEnabled } = useTranslation();
  const { translate } = useTranslationService();
  
  // State for translated content
  const [translatedContent, setTranslatedContent] = useState({
    title: 'Premium IPTV Subscription',
    description: 'Get access to over 10,000 channels, premium VOD content, and 4K streaming quality.',
    features: [
      '10,000+ Live TV Channels',
      '50,000+ Movies & TV Shows', 
      '4K Ultra HD Streaming',
      'Multi-Device Support',
      '24/7 Customer Support',
      'Premium Sports Packages'
    ],
    price: '$19.99',
    originalPrice: '$39.99',
    discount: '50% OFF',
    secureCheckout: 'Secure Checkout',
    orderSummary: 'Order Summary',
    total: 'Total',
    continueButton: 'Continue to Payment',
    guaranteeText: '30-day money-back guarantee',
    secureText: 'SSL encrypted secure payment',
    instantText: 'Instant activation after payment'
  });

  // Translate content when language changes
  useEffect(() => {
    if (!isTranslationEnabled || currentLanguage === 'en') {
      return; // No translation needed
    }

    const translateContent = async () => {
      try {
        const [
          translatedTitle,
          translatedDescription,
          translatedSecureCheckout,
          translatedOrderSummary,
          translatedTotal,
          translatedContinue,
          translatedGuarantee,
          translatedSecure,
          translatedInstant,
          ...translatedFeatures
        ] = await Promise.all([
          translate('Premium IPTV Subscription'),
          translate('Get access to over 10,000 channels, premium VOD content, and 4K streaming quality.'),
          translate('Secure Checkout'),
          translate('Order Summary'),
          translate('Total'),
          translate('Continue to Payment'),
          translate('30-day money-back guarantee'),
          translate('SSL encrypted secure payment'),
          translate('Instant activation after payment'),
          ...translatedContent.features.map(feature => translate(feature))
        ]);

        setTranslatedContent({
          title: translatedTitle,
          description: translatedDescription,
          features: translatedFeatures,
          price: '$19.99', // Keep prices in original format
          originalPrice: '$39.99',
          discount: '50% OFF',
          secureCheckout: translatedSecureCheckout,
          orderSummary: translatedOrderSummary,
          total: translatedTotal,
          continueButton: translatedContinue,
          guaranteeText: translatedGuarantee,
          secureText: translatedSecure,
          instantText: translatedInstant
        });
      } catch (error) {
        console.error('Translation failed:', error);
      }
    };

    translateContent();
  }, [currentLanguage, isTranslationEnabled, translate]);

  const currentLang = languages.find(lang => lang.code === currentLanguage);

  return (
    <div className={`max-w-4xl mx-auto p-6 ${className}`}>
      {/* Header with Language Switcher */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Globe className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold">Translation Demo</h1>
          {currentLang && (
            <Badge variant="outline">
              {currentLang.nativeName} ({currentLang.code.toUpperCase()})
            </Badge>
          )}
        </div>
        {isTranslationEnabled && (
          <LanguageSwitcher 
            variant="outline" 
            size="default"
            showLabel={true}
          />
        )}
      </div>

      {!isTranslationEnabled && (
        <Card className="mb-6 border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <Globe className="h-4 w-4" />
              <span className="text-sm">
                Translation is not enabled. Configure Youdao API credentials in Translation Settings to enable multi-language support.
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Information */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">{translatedContent.title}</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="destructive">{translatedContent.discount}</Badge>
                </div>
              </div>
              <CardDescription>{translatedContent.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {translatedContent.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                <Separator />

                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Shield className="h-4 w-4" />
                    <span>{translatedContent.secureText}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{translatedContent.instantText}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                {translatedContent.orderSummary}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>{translatedContent.title}</span>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground line-through">
                      {translatedContent.originalPrice}
                    </div>
                    <div className="font-semibold">{translatedContent.price}</div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between font-semibold">
                <span>{translatedContent.total}</span>
                <span>{translatedContent.price}</span>
              </div>

              <Button className="w-full" size="lg">
                <CreditCard className="mr-2 h-4 w-4" />
                {translatedContent.continueButton}
              </Button>

              <div className="text-center">
                <div className="text-xs text-muted-foreground">
                  {translatedContent.guaranteeText}
                </div>
              </div>

              <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
                <Shield className="h-3 w-3" />
                <span>{translatedContent.secureCheckout}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Translation Info */}
      {isTranslationEnabled && (
        <Card className="mt-6 border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-blue-800 font-medium">
                <Globe className="h-4 w-4" />
                <span>Translation Active</span>
              </div>
              <div className="text-sm text-blue-700">
                <p>Current Language: <strong>{currentLang?.nativeName} ({currentLang?.name})</strong></p>
                <p>Available Languages: {languages.length}</p>
                <p>This page content is automatically translated using Youdao API when you switch languages.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
