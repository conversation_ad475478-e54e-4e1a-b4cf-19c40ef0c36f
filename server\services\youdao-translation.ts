import crypto from 'crypto';
import axios from 'axios';

export interface YoudaoTranslationRequest {
  q: string;
  from: string;
  to: string;
  appKey: string;
  salt: string;
  sign: string;
  signType: string;
  curtime: string;
}

export interface YoudaoTranslationResponse {
  errorCode: string;
  query: string;
  translation: string[];
  basic?: {
    phonetic?: string;
    explains?: string[];
  };
  web?: Array<{
    key: string;
    value: string[];
  }>;
  l: string;
  dict?: {
    url: string;
  };
  webdict?: {
    url: string;
  };
  tSpeakUrl?: string;
  speakUrl?: string;
}

export interface TranslationConfig {
  appId: string;
  apiKey: string;
}

export class YoudaoTranslationService {
  private readonly baseUrl = 'https://openapi.youdao.com/api';
  private config: TranslationConfig | null = null;

  constructor(config?: TranslationConfig) {
    if (config) {
      this.config = config;
    }
  }

  setConfig(config: TranslationConfig) {
    this.config = config;
  }

  private generateSign(query: string, appKey: string, salt: string, curtime: string, appSecret: string): string {
    const str = appKey + this.truncate(query) + salt + curtime + appSecret;
    return crypto.createHash('sha256').update(str).digest('hex');
  }

  private truncate(q: string): string {
    const len = q.length;
    if (len <= 20) return q;
    return q.substring(0, 10) + len + q.substring(len - 10, len);
  }

  private generateSalt(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  private getCurrentTime(): string {
    return Math.floor(Date.now() / 1000).toString();
  }

  async translate(text: string, from: string = 'auto', to: string = 'en'): Promise<string> {
    if (!this.config) {
      throw new Error('Youdao translation service not configured');
    }

    if (!text || text.trim() === '') {
      return text;
    }

    const salt = this.generateSalt();
    const curtime = this.getCurrentTime();
    const sign = this.generateSign(text, this.config.appId, salt, curtime, this.config.apiKey);

    const params: YoudaoTranslationRequest = {
      q: text,
      from,
      to,
      appKey: this.config.appId,
      salt,
      sign,
      signType: 'v3',
      curtime,
    };

    try {
      console.log('🌐 Youdao API Request:', {
        url: this.baseUrl,
        params: { ...params, sign: '[HIDDEN]' }
      });

      const response = await axios.post(this.baseUrl, new URLSearchParams(params), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 10000,
      });

      const data: YoudaoTranslationResponse = response.data;
      
      console.log('🌐 Youdao API Response:', {
        errorCode: data.errorCode,
        query: data.query,
        translation: data.translation,
        l: data.l
      });

      if (data.errorCode !== '0') {
        throw new Error(`Youdao API error: ${this.getErrorMessage(data.errorCode)}`);
      }

      if (!data.translation || data.translation.length === 0) {
        throw new Error('No translation returned from Youdao API');
      }

      return data.translation[0];
    } catch (error) {
      console.error('❌ Youdao translation error:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }
        throw new Error(`Youdao API request failed: ${error.message}`);
      }
      throw error;
    }
  }

  async testConnection(): Promise<{ success: boolean; message: string; translation?: string }> {
    try {
      const testText = 'Hello, how are you?';
      const translation = await this.translate(testText, 'en', 'zh');
      
      return {
        success: true,
        message: 'Connection successful',
        translation
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private getErrorMessage(errorCode: string): string {
    const errorMessages: Record<string, string> = {
      '101': 'Missing required parameters',
      '102': 'Unsupported language type',
      '103': 'Text too long',
      '104': 'Unsupported API type',
      '105': 'Unsupported signature type',
      '106': 'Unsupported response type',
      '107': 'Unsupported transmission encryption type',
      '108': 'Invalid App Key',
      '109': 'Invalid batchLog format',
      '110': 'No related service',
      '111': 'Developer account is not active',
      '201': 'Decryption failed',
      '202': 'Invalid signature',
      '203': 'Access IP address not in the allowed list',
      '301': 'Dictionary query failed',
      '302': 'Translation query failed',
      '303': 'Server error',
      '401': 'Account balance insufficient',
      '411': 'Access frequency limited',
      '412': 'Long text requests too frequent',
    };

    return errorMessages[errorCode] || `Unknown error code: ${errorCode}`;
  }

  // Language mappings for Youdao API
  static readonly LANGUAGE_MAPPINGS: Record<string, string> = {
    'en': 'en',      // English
    'fr': 'fr',      // French
    'es': 'es',      // Spanish
    'pt': 'pt',      // Portuguese
    'de': 'de',      // German
    'nl': 'nl',      // Dutch
    'pl': 'pl',      // Polish
    'it': 'it',      // Italian
    'sv': 'sv',      // Swedish
    'fi': 'fi',      // Finnish
    'da': 'da',      // Danish
    'ro': 'ro',      // Romanian
    'sq': 'sq',      // Albanian
    'sk': 'sk',      // Slovak
    'no': 'no',      // Norwegian
    'zh': 'zh-CHS',  // Chinese Simplified (default for Chinese)
  };

  static getYoudaoLanguageCode(languageCode: string): string {
    return this.LANGUAGE_MAPPINGS[languageCode] || languageCode;
  }
}

// Default supported languages with their Youdao mappings
export const DEFAULT_SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English', youdaoCode: 'en', isDefault: true },
  { code: 'fr', name: 'French', nativeName: 'Français', youdaoCode: 'fr', isDefault: false },
  { code: 'es', name: 'Spanish', nativeName: 'Español', youdaoCode: 'es', isDefault: false },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português', youdaoCode: 'pt', isDefault: false },
  { code: 'de', name: 'German', nativeName: 'Deutsch', youdaoCode: 'de', isDefault: false },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands', youdaoCode: 'nl', isDefault: false },
  { code: 'pl', name: 'Polish', nativeName: 'Polski', youdaoCode: 'pl', isDefault: false },
  { code: 'it', name: 'Italian', nativeName: 'Italiano', youdaoCode: 'it', isDefault: false },
  { code: 'sv', name: 'Swedish', nativeName: 'Svenska', youdaoCode: 'sv', isDefault: false },
  { code: 'fi', name: 'Finnish', nativeName: 'Suomi', youdaoCode: 'fi', isDefault: false },
  { code: 'da', name: 'Danish', nativeName: 'Dansk', youdaoCode: 'da', isDefault: false },
  { code: 'ro', name: 'Romanian', nativeName: 'Română', youdaoCode: 'ro', isDefault: false },
  { code: 'sq', name: 'Albanian', nativeName: 'Shqip', youdaoCode: 'sq', isDefault: false },
  { code: 'sk', name: 'Slovak', nativeName: 'Slovenčina', youdaoCode: 'sk', isDefault: false },
  { code: 'no', name: 'Norwegian', nativeName: 'Norsk', youdaoCode: 'no', isDefault: false },
];
