import { db } from '../db';
import { sql } from 'drizzle-orm';
import { customCheckoutPages } from '../../shared/schema';

export async function addTranslationTables() {
  try {
    console.log('🔄 Starting translation tables migration...');

    // Create translation_settings table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS translation_settings (
        id INTEGER PRIMARY KEY,
        youdao_app_id TEXT,
        youdao_api_key TEXT,
        enabled INTEGER NOT NULL DEFAULT 0,
        auto_detect_browser_language INTEGER NOT NULL DEFAULT 1,
        default_language TEXT NOT NULL DEFAULT 'en',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create supported_languages table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS supported_languages (
        id INTEGER PRIMARY KEY,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        native_name TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        youdao_code TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create translations table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS translations (
        id INTEGER PRIMARY KEY,
        source_text TEXT NOT NULL,
        target_language TEXT NOT NULL,
        translated_text TEXT NOT NULL,
        source_language TEXT NOT NULL DEFAULT 'en',
        checksum TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create customer_languages table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS customer_languages (
        id INTEGER PRIMARY KEY,
        email TEXT NOT NULL,
        language_code TEXT NOT NULL,
        last_used TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    `);

    // Create indexes for better performance
    await db.run(sql`
      CREATE INDEX IF NOT EXISTS idx_translations_checksum
      ON translations(checksum)
    `);

    await db.run(sql`
      CREATE INDEX IF NOT EXISTS idx_translations_languages
      ON translations(source_language, target_language)
    `);

    await db.run(sql`
      CREATE INDEX IF NOT EXISTS idx_customer_languages_email
      ON customer_languages(email)
    `);

    console.log('✅ Translation tables created successfully');

    // Add translation columns to custom_checkout_pages table
    try {
      await db.run(sql`
        ALTER TABLE custom_checkout_pages
        ADD COLUMN enable_translation INTEGER NOT NULL DEFAULT 0
      `);
      console.log('✅ Added enable_translation column to custom_checkout_pages');
    } catch (error) {
      console.log('ℹ️ enable_translation column already exists or error:', error);
    }

    try {
      await db.run(sql`
        ALTER TABLE custom_checkout_pages
        ADD COLUMN translation_languages TEXT
      `);
      console.log('✅ Added translation_languages column to custom_checkout_pages');
    } catch (error) {
      console.log('ℹ️ translation_languages column already exists or error:', error);
    }

  // Update existing checkout pages to have default translation values
  try {
    // First, let's check if the table exists and has data
    const checkoutPages = await db.select().from(customCheckoutPages);
    console.log(`Found ${checkoutPages.length} existing checkout pages`);

    if (checkoutPages.length > 0) {
      // Update pages that don't have translation fields set
      const result = await db.update(customCheckoutPages)
        .set({
          enableTranslation: 0,
          translationLanguages: '[]'
        })
        .where(sql`enable_translation IS NULL OR translation_languages IS NULL`);

      console.log('✅ Updated existing checkout pages with default translation values');

      // Also ensure all pages have the fields, even if they're already set
      await db.run(sql`
        UPDATE custom_checkout_pages
        SET enable_translation = COALESCE(enable_translation, 0),
            translation_languages = COALESCE(translation_languages, '[]')
      `);
      console.log('✅ Ensured all checkout pages have translation fields');
    }
  } catch (error) {
    console.log('ℹ️ Error updating existing checkout pages:', error);
  }

    // Initialize default languages
    const { translationStorage } = await import('../services/translation-storage');
    await translationStorage.initializeDefaultLanguages();

    console.log('✅ Translation tables migration completed successfully');
  } catch (error) {
    console.error('❌ Error during translation tables migration:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addTranslationTables()
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
