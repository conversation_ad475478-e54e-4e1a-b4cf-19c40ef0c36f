import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addTranslationTables() {
  try {
    console.log('🔄 Starting translation tables migration...');

    // Create translation_settings table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS translation_settings (
        id INTEGER PRIMARY KEY,
        youdao_app_id TEXT,
        youdao_api_key TEXT,
        enabled INTEGER NOT NULL DEFAULT 0,
        auto_detect_browser_language INTEGER NOT NULL DEFAULT 1,
        default_language TEXT NOT NULL DEFAULT 'en',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create supported_languages table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS supported_languages (
        id INTEGER PRIMARY KEY,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        native_name TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        youdao_code TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create translations table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS translations (
        id INTEGER PRIMARY KEY,
        source_text TEXT NOT NULL,
        target_language TEXT NOT NULL,
        translated_text TEXT NOT NULL,
        source_language TEXT NOT NULL DEFAULT 'en',
        checksum TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create customer_languages table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS customer_languages (
        id INTEGER PRIMARY KEY,
        email TEXT NOT NULL,
        language_code TEXT NOT NULL,
        last_used TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    `);

    // Create indexes for better performance
    await db.run(sql`
      CREATE INDEX IF NOT EXISTS idx_translations_checksum
      ON translations(checksum)
    `);

    await db.run(sql`
      CREATE INDEX IF NOT EXISTS idx_translations_languages
      ON translations(source_language, target_language)
    `);

    await db.run(sql`
      CREATE INDEX IF NOT EXISTS idx_customer_languages_email
      ON customer_languages(email)
    `);

    console.log('✅ Translation tables created successfully');

    // Initialize default languages
    const { translationStorage } = await import('../services/translation-storage');
    await translationStorage.initializeDefaultLanguages();

    console.log('✅ Translation tables migration completed successfully');
  } catch (error) {
    console.error('❌ Error during translation tables migration:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addTranslationTables()
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
