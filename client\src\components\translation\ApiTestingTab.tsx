import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, TestTube, CheckCircle, XCircle, ArrowRight, Wifi } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { TranslationSettings, SupportedLanguage } from '@/pages/admin/TranslationSettings';
import { testTranslation, testYoudaoConnection } from '@/pages/admin/TranslationSettings';

const testFormSchema = z.object({
  text: z.string().min(1, 'Text is required'),
  sourceLanguage: z.string().min(1, 'Source language is required'),
  targetLanguage: z.string().min(1, 'Target language is required'),
});

const connectionTestSchema = z.object({
  appId: z.string().optional(),
  apiKey: z.string().optional(),
});

type TestFormValues = z.infer<typeof testFormSchema>;
type ConnectionTestValues = z.infer<typeof connectionTestSchema>;

interface ApiTestingTabProps {
  settings: TranslationSettings | null;
  languages: SupportedLanguage[];
}

export default function ApiTestingTab({ settings, languages }: ApiTestingTabProps) {
  const [testResult, setTestResult] = useState<any>(null);
  const [connectionResult, setConnectionResult] = useState<any>(null);
  const { toast } = useToast();

  const enabledLanguages = languages.filter(lang => lang.enabled);

  const testForm = useForm<TestFormValues>({
    resolver: zodResolver(testFormSchema),
    defaultValues: {
      text: 'Hello, how are you today?',
      sourceLanguage: 'en',
      targetLanguage: 'fr',
    },
  });

  const connectionForm = useForm<ConnectionTestValues>({
    resolver: zodResolver(connectionTestSchema),
    defaultValues: {
      appId: '',
      apiKey: '',
    },
  });

  // Test translation mutation
  const testTranslationMutation = useMutation({
    mutationFn: testTranslation,
    onSuccess: (data) => {
      setTestResult(data);
      toast({
        title: 'Translation Test Successful',
        description: 'The translation API is working correctly',
      });
    },
    onError: (error: any) => {
      setTestResult({ success: false, message: error.message });
      toast({
        title: 'Translation Test Failed',
        description: error.message || 'Failed to test translation',
        variant: 'destructive',
      });
    },
  });

  // Test connection mutation
  const testConnectionMutation = useMutation({
    mutationFn: testYoudaoConnection,
    onSuccess: (data) => {
      setConnectionResult(data);
      if (data.success) {
        toast({
          title: 'Connection Test Successful',
          description: 'Successfully connected to Youdao API',
        });
      } else {
        toast({
          title: 'Connection Test Failed',
          description: data.message,
          variant: 'destructive',
        });
      }
    },
    onError: (error: any) => {
      setConnectionResult({ success: false, message: error.message });
      toast({
        title: 'Connection Test Failed',
        description: error.message || 'Failed to test connection',
        variant: 'destructive',
      });
    },
  });

  const onTestTranslation = (data: TestFormValues) => {
    testTranslationMutation.mutate(data);
  };

  const onTestConnection = (data: ConnectionTestValues) => {
    testConnectionMutation.mutate(data);
  };

  const isConfigured = settings?.youdaoAppId && settings?.youdaoApiKey;

  return (
    <div className="space-y-6">
      {!isConfigured && (
        <Alert>
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            Youdao API credentials are not configured. Please configure them in the General Settings tab first.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="h-5 w-5" />
            Connection Test
          </CardTitle>
          <CardDescription>
            Test the connection to Youdao API with your current or custom credentials
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...connectionForm}>
            <form onSubmit={connectionForm.handleSubmit(onTestConnection)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={connectionForm.control}
                  name="appId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>App ID (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Leave empty to use saved credentials"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Test with different credentials
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={connectionForm.control}
                  name="apiKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Key (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Leave empty to use saved credentials"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Test with different credentials
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                disabled={testConnectionMutation.isPending}
                className="w-full"
              >
                {testConnectionMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Wifi className="mr-2 h-4 w-4" />
                Test Connection
              </Button>

              {connectionResult && (
                <Alert className={connectionResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                  {connectionResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className={connectionResult.success ? 'text-green-800' : 'text-red-800'}>
                        {connectionResult.message}
                      </p>
                      {connectionResult.success && connectionResult.translation && (
                        <div className="text-sm">
                          <strong>Test Translation:</strong> {connectionResult.translation}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Translation Test
          </CardTitle>
          <CardDescription>
            Test translation functionality with custom text and language pairs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...testForm}>
            <form onSubmit={testForm.handleSubmit(onTestTranslation)} className="space-y-4">
              <FormField
                control={testForm.control}
                name="text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Text to Translate</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter text to translate..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the text you want to translate
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={testForm.control}
                  name="sourceLanguage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Source Language</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select source language" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {enabledLanguages.map((lang) => (
                            <SelectItem key={lang.code} value={lang.code}>
                              {lang.name} ({lang.code.toUpperCase()})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={testForm.control}
                  name="targetLanguage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Language</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select target language" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {enabledLanguages.map((lang) => (
                            <SelectItem key={lang.code} value={lang.code}>
                              {lang.name} ({lang.code.toUpperCase()})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                disabled={testTranslationMutation.isPending || !isConfigured}
                className="w-full"
              >
                {testTranslationMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <TestTube className="mr-2 h-4 w-4" />
                Test Translation
              </Button>

              {testResult && (
                <Alert className={testResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription>
                    {testResult.success ? (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{testResult.sourceLanguage.toUpperCase()}</Badge>
                          <ArrowRight className="h-4 w-4" />
                          <Badge variant="outline">{testResult.targetLanguage.toUpperCase()}</Badge>
                        </div>
                        <div className="grid gap-2">
                          <div>
                            <strong>Original:</strong> {testResult.originalText}
                          </div>
                          <div>
                            <strong>Translation:</strong> {testResult.translatedText}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-red-800">{testResult.message}</p>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
