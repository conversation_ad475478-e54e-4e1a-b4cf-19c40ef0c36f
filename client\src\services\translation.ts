import { apiRequest } from '@/lib/queryClient';

interface TranslationCache {
  [key: string]: string;
}

class TranslationService {
  private cache: TranslationCache = {};
  private currentLanguage: string = 'en';
  private isEnabled: boolean = false;

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage() {
    try {
      const cached = localStorage.getItem('translation-cache');
      if (cached) {
        this.cache = JSON.parse(cached);
      }
      
      const language = localStorage.getItem('preferred-language');
      if (language) {
        this.currentLanguage = language;
      }
    } catch (error) {
      console.warn('Failed to load translation cache from storage:', error);
    }
  }

  private saveToStorage() {
    try {
      localStorage.setItem('translation-cache', JSON.stringify(this.cache));
    } catch (error) {
      console.warn('Failed to save translation cache to storage:', error);
    }
  }

  private getCacheKey(text: string, targetLanguage: string, sourceLanguage: string = 'en'): string {
    return `${sourceLanguage}:${targetLanguage}:${text}`;
  }

  setCurrentLanguage(languageCode: string) {
    this.currentLanguage = languageCode;
    localStorage.setItem('preferred-language', languageCode);
  }

  getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  async translateText(
    text: string, 
    targetLanguage?: string, 
    sourceLanguage: string = 'en'
  ): Promise<string> {
    // Use current language if target not specified
    const target = targetLanguage || this.currentLanguage;
    
    // Return original text if translation is disabled or same language
    if (!this.isEnabled || sourceLanguage === target || !text || text.trim() === '') {
      return text;
    }

    // Check cache first
    const cacheKey = this.getCacheKey(text, target, sourceLanguage);
    if (this.cache[cacheKey]) {
      return this.cache[cacheKey];
    }

    try {
      // Call translation API
      const response = await apiRequest('/api/translation/test', 'POST', {
        text,
        targetLanguage: target,
        sourceLanguage,
      });

      if (response.success && response.translatedText) {
        // Cache the translation
        this.cache[cacheKey] = response.translatedText;
        this.saveToStorage();
        return response.translatedText;
      }
    } catch (error) {
      console.warn('Translation failed:', error);
    }

    // Return original text on error
    return text;
  }

  async translateObject<T extends Record<string, any>>(
    obj: T,
    fieldsToTranslate: (keyof T)[],
    targetLanguage?: string,
    sourceLanguage: string = 'en'
  ): Promise<T> {
    const translated = { ...obj };
    
    for (const field of fieldsToTranslate) {
      const value = obj[field];
      if (typeof value === 'string') {
        translated[field] = await this.translateText(value, targetLanguage, sourceLanguage);
      }
    }

    return translated;
  }

  clearCache() {
    this.cache = {};
    localStorage.removeItem('translation-cache');
  }

  // Preload common translations
  async preloadTranslations(texts: string[], targetLanguage?: string) {
    const target = targetLanguage || this.currentLanguage;
    if (!this.isEnabled || target === 'en') return;

    const promises = texts.map(text => this.translateText(text, target));
    await Promise.allSettled(promises);
  }
}

// Export singleton instance
export const translationService = new TranslationService();

// React hook for using translations
export const useTranslationService = () => {
  const translate = async (text: string, targetLanguage?: string) => {
    return translationService.translateText(text, targetLanguage);
  };

  const translateObject = async <T extends Record<string, any>>(
    obj: T,
    fieldsToTranslate: (keyof T)[],
    targetLanguage?: string
  ) => {
    return translationService.translateObject(obj, fieldsToTranslate, targetLanguage);
  };

  return {
    translate,
    translateObject,
    setLanguage: (lang: string) => translationService.setCurrentLanguage(lang),
    getCurrentLanguage: () => translationService.getCurrentLanguage(),
    clearCache: () => translationService.clearCache(),
    preloadTranslations: (texts: string[], targetLanguage?: string) => 
      translationService.preloadTranslations(texts, targetLanguage),
  };
};

// Common translations that might be used across the app
export const COMMON_TRANSLATIONS = {
  // Navigation
  'Home': 'Home',
  'Products': 'Products',
  'Checkout': 'Checkout',
  'Contact': 'Contact',
  
  // Form labels
  'Full Name': 'Full Name',
  'Email Address': 'Email Address',
  'Country': 'Country',
  'Application Type': 'Application Type',
  'MAC Address': 'MAC Address',
  
  // Buttons
  'Continue': 'Continue',
  'Submit': 'Submit',
  'Cancel': 'Cancel',
  'Back': 'Back',
  'Next': 'Next',
  'Save': 'Save',
  'Delete': 'Delete',
  'Edit': 'Edit',
  'View': 'View',
  
  // Status messages
  'Loading...': 'Loading...',
  'Success': 'Success',
  'Error': 'Error',
  'Warning': 'Warning',
  'Information': 'Information',
  
  // Checkout messages
  'Secure Checkout': 'Secure Checkout',
  'Order Summary': 'Order Summary',
  'Payment Information': 'Payment Information',
  'Billing Information': 'Billing Information',
  'Processing your order...': 'Processing your order...',
  'Order completed successfully': 'Order completed successfully',
  
  // Validation messages
  'This field is required': 'This field is required',
  'Please enter a valid email address': 'Please enter a valid email address',
  'Please select a country': 'Please select a country',
  'Please select an application type': 'Please select an application type',
};

// Helper function to get translated common text
export const getTranslatedText = async (key: keyof typeof COMMON_TRANSLATIONS, targetLanguage?: string): Promise<string> => {
  const originalText = COMMON_TRANSLATIONS[key];
  return translationService.translateText(originalText, targetLanguage);
};
