import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, Languages, Settings, TestTube } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import { apiRequest } from '@/lib/queryClient';
import GeneralSettingsTab from '@/components/translation/GeneralSettingsTab';
import LanguageSelectionTab from '@/components/translation/LanguageSelectionTab';
import ApiTestingTab from '@/components/translation/ApiTestingTab';

// Types
export interface TranslationSettings {
  id?: number;
  youdaoAppId?: string;
  youdaoApiKey?: string;
  enabled: boolean;
  autoDetectBrowserLanguage: boolean;
  defaultLanguage: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface SupportedLanguage {
  id: number;
  code: string;
  name: string;
  nativeName: string;
  enabled: boolean;
  isDefault: boolean;
  youdaoCode: string;
  createdAt: string;
  updatedAt: string;
}

// API functions
export const getTranslationSettings = async (): Promise<TranslationSettings | null> => {
  return apiRequest('/api/translation/settings', 'GET');
};

export const updateTranslationSettings = async (data: Partial<TranslationSettings>): Promise<TranslationSettings> => {
  return apiRequest('/api/translation/settings', 'PUT', data);
};

export const getSupportedLanguages = async (): Promise<SupportedLanguage[]> => {
  return apiRequest('/api/translation/languages', 'GET');
};

export const updateLanguageStatus = async (code: string, enabled: boolean): Promise<void> => {
  return apiRequest(`/api/translation/languages/${code}/status`, 'PUT', { enabled });
};

export const testTranslation = async (data: {
  text: string;
  targetLanguage: string;
  sourceLanguage?: string;
}): Promise<{
  success: boolean;
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
}> => {
  return apiRequest('/api/translation/test', 'POST', data);
};

export const testYoudaoConnection = async (data?: {
  appId?: string;
  apiKey?: string;
}): Promise<{
  success: boolean;
  message: string;
  translation?: string;
}> => {
  return apiRequest('/api/translation/test-connection', 'POST', data || {});
};

export const initializeDefaultLanguages = async (): Promise<void> => {
  return apiRequest('/api/translation/initialize', 'POST');
};

export default function TranslationSettings() {
  const [activeTab, setActiveTab] = useState('general');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch translation settings
  const { data: settings, isLoading: settingsLoading } = useQuery({
    queryKey: ['/api/translation/settings'],
    queryFn: getTranslationSettings,
  });

  // Fetch supported languages
  const { data: languages, isLoading: languagesLoading } = useQuery({
    queryKey: ['/api/translation/languages'],
    queryFn: getSupportedLanguages,
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: updateTranslationSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/translation/settings'] });
      toast({
        title: 'Success',
        description: 'Translation settings updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update translation settings',
        variant: 'destructive',
      });
    },
  });

  // Update language status mutation
  const updateLanguageMutation = useMutation({
    mutationFn: ({ code, enabled }: { code: string; enabled: boolean }) =>
      updateLanguageStatus(code, enabled),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/translation/languages'] });
      toast({
        title: 'Success',
        description: 'Language status updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update language status',
        variant: 'destructive',
      });
    },
  });

  // Initialize languages mutation
  const initializeLanguagesMutation = useMutation({
    mutationFn: initializeDefaultLanguages,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/translation/languages'] });
      toast({
        title: 'Success',
        description: 'Default languages initialized successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to initialize default languages',
        variant: 'destructive',
      });
    },
  });

  const isLoading = settingsLoading || languagesLoading;

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  const enabledLanguagesCount = languages?.filter(lang => lang.enabled).length || 0;

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Languages className="h-6 w-6 text-primary" />
              <div>
                <CardTitle className="text-2xl font-bold">Translation Settings</CardTitle>
                <CardDescription>
                  Configure multi-language translation for checkout pages and emails
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={settings?.enabled ? 'default' : 'secondary'}>
                {settings?.enabled ? 'Enabled' : 'Disabled'}
              </Badge>
              {enabledLanguagesCount > 0 && (
                <Badge variant="outline">
                  {enabledLanguagesCount} Languages
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                General Settings
              </TabsTrigger>
              <TabsTrigger value="languages" className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                Language Selection
              </TabsTrigger>
              <TabsTrigger value="testing" className="flex items-center gap-2">
                <TestTube className="h-4 w-4" />
                API Testing
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="mt-6">
              <GeneralSettingsTab
                settings={settings}
                onUpdateSettings={updateSettingsMutation.mutate}
                isUpdating={updateSettingsMutation.isPending}
              />
            </TabsContent>

            <TabsContent value="languages" className="mt-6">
              <LanguageSelectionTab
                languages={languages || []}
                onUpdateLanguage={updateLanguageMutation.mutate}
                onInitializeLanguages={initializeLanguagesMutation.mutate}
                isUpdating={updateLanguageMutation.isPending}
                isInitializing={initializeLanguagesMutation.isPending}
              />
            </TabsContent>

            <TabsContent value="testing" className="mt-6">
              <ApiTestingTab
                settings={settings}
                languages={languages || []}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}
