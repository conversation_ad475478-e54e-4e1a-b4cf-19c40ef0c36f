import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Search, Globe, Plus, Info } from 'lucide-react';
import type { SupportedLanguage } from '@/pages/admin/TranslationSettings';

interface LanguageSelectionTabProps {
  languages: SupportedLanguage[];
  onUpdateLanguage: (data: { code: string; enabled: boolean }) => void;
  onInitializeLanguages: () => void;
  isUpdating: boolean;
  isInitializing: boolean;
}

export default function LanguageSelectionTab({
  languages,
  onUpdateLanguage,
  onInitializeLanguages,
  isUpdating,
  isInitializing,
}: LanguageSelectionTabProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredLanguages = languages.filter(
    (lang) =>
      lang.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lang.nativeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lang.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const enabledCount = languages.filter(lang => lang.enabled).length;
  const totalCount = languages.length;

  const handleToggleLanguage = (code: string, enabled: boolean) => {
    onUpdateLanguage({ code, enabled });
  };

  return (
    <div className="space-y-6">
      {languages.length === 0 ? (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>No languages configured. Initialize default languages to get started.</span>
            <Button
              onClick={onInitializeLanguages}
              disabled={isInitializing}
              size="sm"
            >
              {isInitializing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Plus className="mr-2 h-4 w-4" />
              Initialize Languages
            </Button>
          </AlertDescription>
        </Alert>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 min-w-[300px]">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search languages..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {enabledCount} of {totalCount} enabled
                </Badge>
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Supported Languages
              </CardTitle>
              <CardDescription>
                Enable or disable languages for translation. Customers will see a language selector for enabled languages.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredLanguages.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No languages found matching your search.
                  </div>
                ) : (
                  <div className="grid gap-4">
                    {filteredLanguages.map((language) => (
                      <div
                        key={language.code}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-4">
                          <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{language.name}</span>
                              <Badge variant="secondary" className="text-xs">
                                {language.code.toUpperCase()}
                              </Badge>
                              {language.isDefault && (
                                <Badge variant="default" className="text-xs">
                                  Default
                                </Badge>
                              )}
                            </div>
                            <span className="text-sm text-muted-foreground">
                              {language.nativeName}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm text-muted-foreground">
                            Youdao: {language.youdaoCode}
                          </span>
                          <Switch
                            checked={language.enabled}
                            onCheckedChange={(enabled) =>
                              handleToggleLanguage(language.code, enabled)
                            }
                            disabled={isUpdating}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {languages.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>
                    <strong>Language Configuration:</strong>
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Enabled languages will appear in the language selector on checkout pages</li>
                    <li>The default language is used when auto-detection fails</li>
                    <li>Youdao codes are used for API translation requests</li>
                    <li>Translations are cached to improve performance and reduce API costs</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </>
      )}
    </div>
  );
}
